# coding: utf-8
import pymysql
import psycopg2
import pandas as pd
from db_config import DATABASE_TABLES, get_gmv_table, get_activity_table
# 获取数仓原始数据
def get_row_data(sql):
    conn = psycopg2.connect(
        host="hgpostcn-cn-wwo3iu4fu008-cn-shanghai.hologres.aliyuncs.com",  # Hologres实例的网络地址
        port=80,  # Hologres实例端口
        dbname="hologres_prd",  # Hologres数据库名称
        user="LTAI5tJiSFcRXENKTP7NdVbg",  # 阿里云账号的AccessKey ID
        password="******************************",  # 阿里云账号的AccessKey Secret
        keepalives=1,  # 使用长连接（可选，推荐）
        keepalives_idle=130,  # 空闲时保持连接的时间间隔
        keepalives_interval=10,  # 未收到响应时重试间隔
        keepalives_count=15,  # 最大重试次数
    )
    cursor = conn.cursor()
    try:
        df = pd.read_sql(sql,conn)
        return df
    except Exception as e:
        # print(sql)
        print("sql有问题：",e)
        # 返回空的DataFrame而不是None
        return pd.DataFrame()
    finally:
        # 关闭游标和连接
        cursor.close()
        conn.close()

# def get_dim_result(dim, brand, tar_month):
#     conn = psycopg2.connect(
#         host="hgpostcn-cn-wwo3iu4fu008-cn-shanghai.hologres.aliyuncs.com",
#         port=80,
#         dbname="hologres_prd",
#         user="LTAI5tJiSFcRXENKTP7NdVbg",
#         password="******************************",
#         keepalives=1,
#         keepalives_idle=130,
#         keepalives_interval=10,
#         keepalives_count=15,
#     )
#     try:
#         cur = conn.cursor()
#         table_map = {
#             '百事食品': 'dws_o2o_sales_d_bs',
#             '通用磨坊': 'dws_o2o_sales_d_tymf'
#         }
#         table_name = table_map.get(brand)
#
#         if not table_name:
#             raise ValueError(f"品牌 {brand} 未知")
#         sql = f"""
#             SELECT {dim}
#             FROM {table_name}
#             WHERE ds LIKE %s
#             GROUP BY {dim};
#         """
#         cur.execute(sql, (f'%{tar_month}%',))
#         result = cur.fetchall()
#         result_list = [row[0] for row in result]
#     except Exception as e:
#         print(f"Error occurred: {e}")
#         conn.rollback()
#         return None
#     finally:
#         cur.close()
#         conn.close()
#     return result_list

def get_dim_result(flag, dim, brand, tar_date,platform,upc):
    conn = psycopg2.connect(
        host="hgpostcn-cn-wwo3iu4fu008-cn-shanghai.hologres.aliyuncs.com",
        port=80,
        dbname="hologres_prd",
        user="LTAI5tJiSFcRXENKTP7NdVbg",
        password="******************************",
        keepalives=1,
        keepalives_idle=130,
        keepalives_interval=10,
        keepalives_count=15,
    )
    try:
        cur = conn.cursor()
        # table_map = {
        #     '百事食品': 'dws_o2o_sales_d_pepsi_ymd',
        #     '通用磨坊': 'dws_o2o_sales_d_general_mills_ymd',
        #     '水井坊': 'dws_o2o_sales_d_swellfun_ymd',
        #     '太太乐': 'dws_o2o_sales_d_totole_ymd',
        #     '科赴': 'dws_o2o_sales_d_kenvue_ymd',
        #     '上好佳': 'dws_o2o_sales_d_oishi_ymd',
        #     '可口可乐': 'dws_o2o_sales_d_coca_ymd'  
        # }
        # table_name = table_map.get(brand)


        if flag == 1:
            sql = f"""
                SELECT {dim} 
                FROM {get_gmv_table(brand)}
                WHERE ds = '{tar_date}'
                and brand = '{brand}'
                and platform = '{platform}'
                and upc = '{upc}'
                GROUP BY {dim};
            """
        elif flag == 2:
            tar_start_date, tar_end_date = tar_date.split('至')[0], tar_date.split('至')[1]
            sql = f"""
                SELECT {dim} 
                FROM {get_gmv_table(brand)} 
                WHERE ds between '{tar_start_date}' and '{tar_end_date}'
                and platform = '{platform}'
                and brand = '{brand}'
                and upc = '{upc}'
                GROUP BY {dim};
            """
        # print(sql)
        cur.execute(sql)
        result = cur.fetchall()
        result_list = [row[0] for row in result]
    except Exception as e:
        print(f"Error occurred: {e}")
        conn.rollback()
        return None
    finally:
        cur.close()
        conn.close()
    return result_list

# def get_activity_dim_result(flag, dim, brand, tar_date, base_date):
#     conn = psycopg2.connect(
#         host="hgpostcn-cn-wwo3iu4fu008-cn-shanghai.hologres.aliyuncs.com",
#         port=80,
#         dbname="hologres_prd",
#         user="LTAI5tJiSFcRXENKTP7NdVbg",
#         password="******************************",
#         keepalives=1,
#         keepalives_idle=130,
#         keepalives_interval=10,
#         keepalives_count=15,
#     )
#     try:
#         cur = conn.cursor()
#         table_name = 'dm_o2o_activity_detail_da'
#         if flag == 1:
#             trim_tar_date = tar_date.replace('-', '')
#             sql = f"""
#                 SELECT {dim} 
#                 FROM {table_name} 
#                 WHERE ds = '{trim_tar_date}'
#                 and platform = '美团'
#                 and brand = '{brand}'
#                 GROUP BY {dim};
#             """
#         elif flag == 2:
#             tar_start_date, tar_end_date = tar_date.split('至')[0], tar_date.split('至')[1]
#             base_start_date, base_end_date = base_date.split('至')[0], tar_date.split('至')[1]
#             trim_tar_start_date = tar_start_date.replace('-', '')
#             trim_tar_end_date = tar_end_date.replace('-', '')
#             trim_base_start_date = base_start_date.replace('-', '')
#             trim_base_end_date = base_end_date.replace('-', '')
#             sql = f"""
#                 SELECT {dim} 
#                 FROM {table_name} 
#                 WHERE (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
#                 and platform = '美团'
#                 and brand = '{brand}'
#                 GROUP BY {dim};
#             """
#         # print(sql)
#         cur.execute(sql)
#         result = cur.fetchall()
#         result_list = [row[0] for row in result]
#     except Exception as e:
#         print(f"Error occurred: {e}")
#         conn.rollback()
#         return None
#     finally:
#         cur.close()
#         conn.close()
#     return result_list