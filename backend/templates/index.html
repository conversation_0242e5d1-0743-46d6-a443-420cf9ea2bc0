<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>归因分析工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://npmcdn.com/flatpickr/dist/l10n/zh.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        body {
            margin: 20px;
        }
        .container {
            max-width: 2200px !important;  /* 与输出框宽度一致 */
            margin-left: 0 !important;     /* 移除左边距 */
            padding-left: 20px !important; /* 添加适当的左内边距 */
        }
        .output-box {
            width: 2200px;
            height: 300px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 0px;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .date-container, .compare-container, .filter-container {
            margin-bottom: 20px;
        }
        .filter-container {
            display: flex;
            gap: 20px;
        }
        .sheet-container {
            margin-top: 20px;
        }
        .sheet-tabs {
            margin-bottom: 10px;
        }
        .btn-outline-primary {
            color: #6c757d;
            border: 1px solid;
            background-color: transparent;
            border-radius: 0;
            transition: all 0.3s ease;
            padding: 5px 10px;
            margin-right: 10px;
        }

        .btn-check:checked + .btn-outline-primary {
            color: #007bff;
            font-weight: bold;
            border-color: #007bff;
            background-color: white;
        }

        .btn-outline-primary:hover {
            color: #0056b3;
            background-color: #f8f9fa;
        }

        .btn-check:checked + .btn-outline-primary:hover {
            background-color: white;
            color: #007bff;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4" style="text-align: left; width: 763px; height: 28px; margin: 0; line-height: 28px; font-size: 20px;">归因分析工具</h1>

        <div style="margin-bottom: 20px; margin-top: 20px;">
            <div class="btn-group" role="group">
                <label for="attr_index" class="form-label" style="margin-right: 16px; height: 28px; width: 112px; line-height: 28px; font-size: 16px; display: inline-block;">归因日期类型：</label>
                <input type="radio" class="btn-check" name="date_type" id="single_date" value="single" checked>
                <label class="btn btn-outline-primary" for="single_date" style="margin-right: 0; height: 28px; line-height: 16px; font-size: 16px;">单日日期</label>

                <input type="radio" class="btn-check" name="date_type" id="range_date" value="range">
                <label class="btn btn-outline-primary" for="range_date" style="height: 28px; line-height: 16px; font-size: 16px;">连续日期</label>
            </div>
        </div>

        <div id="single_date_container">
            <div style="display: flex; align-items: center; margin-top: 20px;">
                <label for="tar_date" class="form-label" style="margin-bottom: 0; height: 28px; width: 80px; line-height: 28px; font-size: 16px; display: inline-block;">目标日期：</label>
                <div style="position: relative; width: 200px;">
                    <input type="text" id="tar_date" class="form-control date-picker"
                           placeholder="选择日期"
                           style="width: 100%;
                               border: 2px solid;
                               border-radius: 0px;
                               padding: 5px 10px;
                               padding-right: 30px;
                               font-size: 16px;
                               height: 28px;
                               line-height: 16px;
                               box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2);
                               transition: border-color 0.3s, box-shadow 0.3s;">
                    <i class="bi bi-calendar3" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;"></i>
                </div>
            </div>

            <div class="compare-container">
                <br>
                <label class="form-label" style="margin-right: 10px; height: 28px; width: 80px; line-height: 28px; font-size: 16px; display: inline-block;">对比类型：</label>
                <div class="btn-group" role="group">
                    <input type="radio" class="btn-check" name="compare_type" id="compare_huanbi" value="环比" checked>
                    <label class="btn btn-outline-primary" for="compare_huanbi" style="margin-right: 0; height: 28px; line-height: 16px; font-size: 16px;">环比</label>

                    <input type="radio" class="btn-check" name="compare_type" id="compare_tongbi" value="同比">
                    <label class="btn btn-outline-primary" for="compare_tongbi" style="height: 28px; line-height: 16px; font-size: 16px;">同比</label>
                </div>

                <div class="compare-hint mt-2" style="font-size: 14px; color: #6c757d;">
                    <p>环比定义：选定目标日期所属周的上一周同一天，如目标日期为本周五，则环比上一周的周五</p>
                    <p>同比定义：选定目标日期所属全年周次+天次的前一年同周次+天，如目标日期为本年第28周的周五，则同比去年第28周的周五</p>
                </div>
            </div>
        </div>

        <div id="range_date_container" class="hidden">
            <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px; margin-top: 20px;">
                <label class="form-label" style="margin-bottom: 0; height: 28px; width: 112px; line-height: 28px; font-size: 16px; display: inline-block;">目标日期范围：</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div style="position: relative; width: 200px;">
                        <input type="text" id="tar_start_date" class="form-control date-picker"
                               placeholder="开始日期"
                               style="width: 100%; border: 2px solid; border-radius: 0px; padding-right: 30px; font-size: 16px; height: 28px; line-height: 16px;">
                        <i class="bi bi-calendar3" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;"></i>
                    </div>
                    <span style="font-size: 16px;">至</span>
                    <div style="position: relative; width: 200px;">
                        <input type="text" id="tar_end_date" class="form-control date-picker"
                               placeholder="结束日期"
                               style="width: 100%; border: 2px solid; border-radius: 0px; padding-right: 30px; font-size: 16px; height: 28px; line-height: 16px;">
                        <i class="bi bi-calendar3" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;"></i>
                    </div>
                    <span id="tar_date_info" style="font-size: 14px; color: gray;"></span>
                </div>
            </div>

            <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
                <label class="form-label" style="margin-bottom: 0; height: 28px; width: 112px; line-height: 28px; font-size: 16px; display: inline-block;">对比日期范围：</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div style="position: relative; width: 200px;">
                        <input type="text" id="base_start_date" class="form-control date-picker"
                               placeholder="开始日期"
                               style="width: 100%; border: 2px solid; border-radius: 0px; padding-right: 30px; font-size: 16px; height: 28px; line-height: 16px;">
                        <i class="bi bi-calendar3" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;"></i>
                    </div>
                    <span style="font-size: 16px;">至</span>
                    <div style="position: relative; width: 200px;">
                        <input type="text" id="base_end_date" class="form-control date-picker"
                               placeholder="结束日期"
                               style="width: 100%; border: 2px solid; border-radius: 0px; padding-right: 30px; font-size: 16px; height: 28px; line-height: 16px;">
                        <i class="bi bi-calendar3" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;"></i>
                    </div>
                    <span id="base_date_info" style="font-size: 14px; color: gray;"></span>
                </div>
            </div>
        </div>

        <div style="height: 1px; width: 940px; background-color: #ccc;"></div>

        <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px; margin-top: 20px;">
            <div style="display: flex; align-items: center;">
                <label for="attr_index" class="form-label" style="margin-right: 10px; height: 28px; width: 80px; line-height: 28px; font-size: 16px; display: inline-block;">归因指标：</label>
                <select id="attr_index" class="form-select"
                        style="width: 150px; border: 2px solid; border-radius: 0px; padding: 5px 10px; font-size: 14px; height: 28px; line-height: 16px; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                    <option value="全量GMV" style="font-size: 14px;">全量GMV</option>
                    <option value="活动GMV" style="font-size: 14px;">活动GMV</option>
                </select>
            </div>
        </div>

        <div style="height: 1px; width: 940px; background-color: #ccc;"></div>

        <div class="filter-container" style="margin-top: 20px;">
            <div style="display: flex; align-items: center; gap: 20px;">
                <div style="display: flex; align-items: center;">
                    <label for="sub_brand" class="form-label" style="margin-right: 15px; height: 28px; width: 80px; line-height: 28px; font-size: 16px; display: inline-block;">筛选维度：</label>
                    <label for="sub_brand" class="form-label" style="margin-right: 5px; height: 28px; line-height: 28px; font-size: 16px;">子品牌：</label>
                    <select id="sub_brand" class="form-select"
                            style="width: 150px; border: 2px solid; border-radius: 0px; padding: 5px 10px; font-size: 14px; height: 28px; line-height: 16px; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                        <option value="全部" style="font-size: 14px;">全部</option>
                        <option value="多力多滋">多力多滋</option>
                        <option value="一口阳光">一口阳光</option>
                        <option value="乐事">乐事</option>
                        <option value="百草味">百草味</option>
                        <option value="桂格">桂格</option>
                        <option value="奇多">奇多</option>
                    </select>
                </div>

                <div style="display: flex; align-items: center;">
                    <label for="province" class="form-label" style="margin-left: 60px; margin-right: 10px; height: 28px; line-height: 28px; font-size: 16px;">省份：</label>
                    <select id="province" class="form-select"
                            style="width: 150px; border: 2px solid; border-radius: 0px; padding: 5px 10px; font-size: 14px; height: 28px; line-height: 16px; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                        <option value="全部" style="font-size: 14px;">全部</option>
                        <option value="安徽省">安徽省</option>
                        <option value="北京市">北京市</option>
                        <option value="重庆市">重庆市</option>
                        <option value="福建省">福建省</option>
                        <option value="甘肃省">甘肃省</option>
                        <option value="广东省">广东省</option>
                        <option value="广西壮族自治区">广西壮族自治区</option>
                        <option value="贵州省">贵州省</option>
                        <option value="海南省">海南省</option>
                        <option value="河北省">河北省</option>
                        <option value="河南省">河南省</option>
                        <option value="黑龙江省">黑龙江省</option>
                        <option value="湖北省">湖北省</option>
                        <option value="湖南省">湖南省</option>
                        <option value="吉林省">吉林省</option>
                        <option value="江苏省">江苏省</option>
                        <option value="江西省">江西省</option>
                        <option value="辽宁省">辽宁省</option>
                        <option value="内蒙古自治区">内蒙古自治区</option>
                        <option value="宁夏回族自治区">宁夏回族自治区</option>
                        <option value="青海省">青海省</option>
                        <option value="山东省">山东省</option>
                        <option value="山西省">山西省</option>
                        <option value="陕西省">陕西省</option>
                        <option value="上海市">上海市</option>
                        <option value="四川省">四川省</option>
                        <option value="天津市">天津市</option>
                        <option value="西藏自治区">西藏自治区</option>
                        <option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
                        <option value="云南省">云南省</option>
                        <option value="浙江省">浙江省</option>
                    </select>
                </div>

                <div style="display: flex; align-items: center;">
                    <label for="retailer" class="form-label" style="margin-left: 60px; margin-right: 10px; height: 28px; line-height: 28px; font-size: 16px;">零售商：</label>
                    <select id="retailer" class="form-select"
                            style="width: 150px; border: 2px solid; border-radius: 0px; padding: 5px 10px; font-size: 14px; height: 28px; line-height: 16px; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                        <option value="全部" style="font-size: 14px;">全部</option>
                        <option value="711">711</option>
                        <option value="百汇">百汇</option>
                        <option value="大润发">大润发</option>
                        <option value="共橙">共橙</option>
                        <option value="好特卖">好特卖</option>
                        <option value="华润">华润</option>
                        <option value="惠宜选">惠宜选</option>
                        <option value="吉慕">吉慕</option>
                        <option value="佳佳">佳佳</option>
                        <option value="佳美">佳美</option>
                        <option value="家家悦">家家悦</option>
                        <option value="江小囤">江小囤</option>
                        <option value="京东便利店">京东便利店</option>
                        <option value="昆仑好客">昆仑好客</option>
                        <option value="懒猫超市">懒猫超市</option>
                        <option value="零食有鸣">零食有鸣</option>
                        <option value="罗森">罗森</option>
                        <option value="美好超市">美好超市</option>
                        <option value="美团买菜">美团买菜</option>
                        <option value="美宜佳">美宜佳</option>
                        <option value="全家便利店">全家便利店</option>
                        <option value="散店-便利店">散店-便利店</option>
                        <option value="散店-超市">散店-超市</option>
                        <option value="松鼠单体加盟">松鼠单体加盟</option>
                        <option value="万辉">万辉</option>
                        <option value="文峰千家惠">文峰千家惠</option>
                        <option value="沃尔玛">沃尔玛</option>
                        <option value="物美">物美</option>
                        <option value="小柴购">小柴购</option>
                        <option value="熊猫很忙">熊猫很忙</option>
                        <option value="永辉">永辉</option>
                        <option value="优购哆">优购哆</option>
                    </select>
                </div>
            </div>
        </div>
        <div id="coupon_container" style="display: none;">
            <div class="filter-container" style="margin-top: 20px;">
                <div style="display: flex; align-items: center; gap: 20px;">
                    <div style="display: flex; align-items: center;">
                        <label for="coupon_mechanism" class="form-label" style="margin-left: 105px; margin-right: 5px; height: 28px; line-height: 28px; font-size: 16px;">券机制：</label>
                        <select id="coupon_mechanism" class="form-select"
                                style="width: 150px; border: 2px solid; border-radius: 0px; padding: 5px 10px; font-size: 14px; height: 28px; line-height: 16px; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                            <option value="全部" style="font-size: 14px;">全部</option>
                        </select>
                    </div>

                    <div style="display: flex; align-items: center;">
                        <label for="coupon_threshold" class="form-label" style="margin-left: 47px; margin-right: 5px; height: 28px; line-height: 28px; font-size: 16px;">券门槛：</label>
                        <select id="coupon_threshold" class="form-select"
                                style="width: 150px; border: 2px solid; border-radius: 0px; padding: 5px 10px; font-size: 14px; height: 28px; line-height: 16px; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                            <option value="全部" style="font-size: 14px;">全部</option>
                        </select>
                    </div>

                    <div style="display: flex; align-items: center;">
                        <label for="coupon_discount" class="form-label" style="margin-left: 40px; margin-right: 10px; height: 28px; line-height: 28px; font-size: 16px;">优惠力度：</label>
                        <select id="coupon_discount" class="form-select"
                                style="width: 150px; border: 2px solid; border-radius: 0px; padding: 5px 10px; font-size: 14px; height: 28px; line-height: 16px; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                            <option value="全部" style="font-size: 14px;">全部</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div style="height: 1px; width: 940px; background-color: #ccc;"></div>

        <div class="container mt-4 d-flex align-items-center">
            <button id="run_analysis" class="btn btn-primary me-3">AI归因分析</button>

            <div id="progressContainer" class="progress" style="display: none; width: 200px; height: 25px;">
                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                     role="progressbar" style="width: 0%; background-color: #3366FF;"
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    0%
                </div>
            </div>
        </div>

        <div id="output" class="output-box mt-3"></div>

        <div class="content-wrapper" style="width: 2200px; margin-left: 0;">
            <br>
            <div class="compare-hint mt-2" style="font-size: 14px; color: #6c757d;">
                <p>贡献度：某维度数据的波动对整体指标波动的贡献比例，其中当大盘波动为正向时，各项贡献度之和为+100%；当大盘波动为负向时，各项贡献度之和为-100%，反映其对整体波动的相对影响程度。</p>
            </div>
            <div id="sheetHeader" style="display: flex; justify-content: space-between; align-items: center; width: 100%; margin-bottom: 10px;">
                <ul class="nav nav-tabs sheet-tabs" id="sheetTabs" role="tablist">
                    <!-- 动态生成 Sheet 标签 -->
                </ul>
                <button id="download_excel" class="btn btn-secondary" style="display: none;">下载完整结果</button>
            </div>

            <div class="sheet-container" style="width: 2200px;">
                <div class="tab-content" id="sheetContent" style="width: 100%;">
                    <!-- 动态生成表格 -->
                </div>
            </div>
        </div>

    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function () {
            // 日期格式转换函数
            function formatDateToYYYYMMDD(dateInput) {
                if (!dateInput) return '';

                // 如果是字符串
                if (typeof dateInput === 'string') {
                    // 如果已经是 YYYYMMDD 格式，直接返回
                    if (/^\d{8}$/.test(dateInput)) {
                        return dateInput;
                    }
                    // 如果是 YYYY-MM-DD 格式，移除连字符
                    if (/^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
                        return dateInput.replace(/-/g, '');
                    }
                    // 如果是完整的日期时间字符串，尝试解析
                    try {
                        const parsedDate = new Date(dateInput);
                        if (!isNaN(parsedDate.getTime())) {
                            // 使用 UTC 方法避免时区转换问题
                            const year = parsedDate.getUTCFullYear();
                            const month = String(parsedDate.getUTCMonth() + 1).padStart(2, '0');
                            const day = String(parsedDate.getUTCDate()).padStart(2, '0');
                            return `${year}${month}${day}`;
                        }
                    } catch (e) {
                        // 如果解析失败，继续下面的处理
                    }
                }

                // 如果是 Date 对象
                if (dateInput instanceof Date) {
                    const year = dateInput.getFullYear();
                    const month = String(dateInput.getMonth() + 1).padStart(2, '0');
                    const day = String(dateInput.getDate()).padStart(2, '0');
                    return `${year}${month}${day}`;
                }

                // 如果无法识别格式，尝试转换为字符串并移除连字符
                const stringValue = String(dateInput);
                // 如果字符串包含日期时间格式，尝试解析
                if (stringValue.includes(',') || stringValue.includes('GMT') || stringValue.includes('UTC')) {
                    try {
                        const parsedDate = new Date(stringValue);
                        if (!isNaN(parsedDate.getTime())) {
                            // 使用 UTC 方法避免时区转换问题
                            const year = parsedDate.getUTCFullYear();
                            const month = String(parsedDate.getUTCMonth() + 1).padStart(2, '0');
                            const day = String(parsedDate.getUTCDate()).padStart(2, '0');
                            return `${year}${month}${day}`;
                        }
                    } catch (e) {
                        // 如果解析失败，使用原来的逻辑
                    }
                }
                return stringValue.replace(/-/g, '');
            }
            
            // 日期范围验证和天数提示函数
            function updateDateRangeInfo(startDateId, endDateId, infoId) {
                const startDate = $(startDateId).val();
                const endDate = $(endDateId).val();

                if (startDate && endDate) {
                    const start = new Date(startDate);
                    const end = new Date(endDate);

                    if (start > end) {
                        alert("开始日期不能大于结束日期！");
                        $(endDateId).val('');
                        $(infoId).text('');
                    } else {
                        const timeDiff = Math.abs(end - start);
                        const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // 包含结束日期
                        $(infoId).text(`共${diffDays}天`);
                    }
                } else {
                    $(infoId).text('');
                }
            }

            // 切换日期类型时重置归因指标
            $('input[name="date_type"]').change(function () {
                if ($('#attr_index').val() === '活动GMV') {
                    $('#attr_index').val('全量GMV');
                    $('#coupon_container').hide();
                }

                if ($('#single_date').is(':checked')) {
                    $('#single_date_container').removeClass('hidden');
                    $('#range_date_container').addClass('hidden');
                    $('#base_date_range').closest('div').addClass('hidden');
                } else {
                    $('#single_date_container').addClass('hidden');
                    $('#range_date_container').removeClass('hidden');
                    $('#base_date_range').closest('div').removeClass('hidden');
                }
            });

            // 监听单日期变化
            $("#tar_date").change(function() {
                if ($('#attr_index').val() === '活动GMV') {
                    $('#attr_index').val('全量GMV');
                    $('#coupon_container').hide();
                }
            });

            // 监听日期范围变化
            $("#tar_start_date, #tar_end_date").change(function () {
                if ($('#attr_index').val() === '活动GMV') {
                    $('#attr_index').val('全量GMV');
                    $('#coupon_container').hide();
                }
                updateDateRangeInfo("#tar_start_date", "#tar_end_date", "#tar_date_info");
            });

            $("#base_start_date, #base_end_date").change(function () {
                if ($('#attr_index').val() === '活动GMV') {
                    $('#attr_index').val('全量GMV');
                    $('#coupon_container').hide();
                }
                updateDateRangeInfo("#base_start_date", "#base_end_date", "#base_date_info");
            });

            // 监听归因指标的变化
            $('#attr_index').change(function () {
                if ($(this).val() === '活动GMV') {
                    // 获取日期信息
                    const dateType = $("input[name='date_type']:checked").val();
                    let hasValidDate = false;

                    if (dateType === 'single') {
                        const tar_date = $("#tar_date").val();
                        if (!tar_date) {
                            alert("请选择目标日期！");
                            $(this).val('全量GMV');
                            return;
                        }
                        hasValidDate = true;
                    } else {
                        const tar_start_date = $("#tar_start_date").val();
                        const tar_end_date = $("#tar_end_date").val();
                        const base_start_date = $("#base_start_date").val();
                        const base_end_date = $("#base_end_date").val();

                        if (!tar_start_date || !tar_end_date || !base_start_date || !base_end_date) {
                            alert("请选择完整的日期范围！");
                            $(this).val('全量GMV');
                            return;
                        }
                        hasValidDate = true;
                    }

                    // 只有在日期有效的情况下才显示券相关选项并获取数据
                    if (hasValidDate) {
                        $('#coupon_container').show();
                        let dateData = {};

                        if (dateType === 'single') {
                            // 将YYYY-MM-DD转换为YYYYMMDD
                            dateData = { tar_date: $("#tar_date").val().replace(/-/g, '') };
                        } else {
                            // 将YYYY-MM-DD转换为YYYYMMDD
                            dateData = {
                                tar_start_date: $("#tar_start_date").val().replace(/-/g, ''),
                                tar_end_date: $("#tar_end_date").val().replace(/-/g, ''),
                                base_start_date: $("#base_start_date").val().replace(/-/g, ''),
                                base_end_date: $("#base_end_date").val().replace(/-/g, '')
                            };
                        }

                        // 请求后端接口获取券机制列表
                        $.get('/get_coupon_mechanisms', dateData, function (data) {
                            const couponSelect = $('#coupon_mechanism');
                            couponSelect.empty();
                            couponSelect.append('<option value="全部">全部</option>');
                            data.forEach(function (item) {
                                couponSelect.append(`<option value="${item}">${item}</option>`);
                            });
                        });

                        // 请求后端接口获取券门槛列表
                        $.get('/get_coupon_threshold', dateData, function (data) {
                            const thresholdSelect = $('#coupon_threshold');
                            thresholdSelect.empty();
                            thresholdSelect.append('<option value="全部">全部</option>');
                            data.forEach(function (item) {
                                thresholdSelect.append(`<option value="${item}">${item}</option>`);
                            });
                        });

                        // 请求后端接口获取券折扣列表
                        $.get('/get_coupon_discount', dateData, function (data) {
                            const discountSelect = $('#coupon_discount');
                            discountSelect.empty();
                            discountSelect.append('<option value="全部">全部</option>');
                            data.forEach(function (item) {
                                discountSelect.append(`<option value="${item}">${item}</option>`);
                            });
                        });
                    }
                } else {
                    $('#coupon_container').hide();
                }
            });

            $("#run_analysis").click(function () {
                const dateType = $("input[name='date_type']:checked").val();
                const compare_type = $("input[name='compare_type']:checked").val();
                const sub_brand = $("#sub_brand").val();
                const province = $("#province").val();
                const retailer = $("#retailer").val();
                const attr_index = $("#attr_index").val();
                const coupon_mechanism = $("#coupon_mechanism").val();
                const coupon_threshold = $("#coupon_threshold").val();
                const coupon_discount = $("#coupon_discount").val();
                
                // 获取品牌信息，从URL参数中获取
                const urlParams = new URLSearchParams(window.location.search);
                const brand = urlParams.get('brand') || '全部';

                let data = { 
                    compare_type, 
                    sub_brand, 
                    province, 
                    retailer, 
                    attr_index, 
                    coupon_mechanism, 
                    coupon_threshold, 
                    coupon_discount,
                    brand  // 添加品牌参数
                };

                if (dateType === 'single') {
                    const tar_date = $("#tar_date").val();
                    if (!tar_date) {
                        alert("请选择目标日期！");
                        return;
                    }
                    // 将YYYY-MM-DD转换为YYYYMMDD
                    data.tar_date = tar_date.replace(/-/g, '');
                } else {
                    const tar_start_date = $("#tar_start_date").val();
                    const tar_end_date = $("#tar_end_date").val();
                    const base_start_date = $("#base_start_date").val();
                    const base_end_date = $("#base_end_date").val();

                    if (!tar_start_date || !tar_end_date || !base_start_date || !base_end_date) {
                        alert("请选择完整的日期范围！");
                        return;
                    }

                    // 将YYYY-MM-DD转换为YYYYMMDD
                    data.tar_start_date = tar_start_date.replace(/-/g, '');
                    data.tar_end_date = tar_end_date.replace(/-/g, '');
                    data.base_start_date = base_start_date.replace(/-/g, '');
                    data.base_end_date = base_end_date.replace(/-/g, '');
                }

                // 进度条逻辑
                const progressBar = $("#progressBar");
                progressBar.css("width", "0%").text("0%");
                $("#progressContainer").show();

                let progress = 0;
                const maxTime = 20000;
                const intervalTime = 200;
                const step = (100 / (maxTime / intervalTime));

                const interval = setInterval(() => {
                    if (progress < 100) {
                        progress += step;
                        progressBar.css("width", `${progress}%`).text(`${Math.floor(progress)}%`);
                    } else {
                        clearInterval(interval);
                    }
                }, intervalTime);

                $.post("/run_attribution", data, function (response) {
                    clearInterval(interval);

                    progressBar.css("width", "100%").text("100%");

                    setTimeout(() => {
                        $("#progressContainer").hide();
                    }, 1000);

                    if (response.status === "success") {
                        const marketData = response.market_data || "无大盘数据";
                        const analysisResult = response.result || "无分析结果";

                        function highlightPercentages(text) {
                            return text.replace(/([-+]\d+(\.\d+)?(%)?)/g, function (match) {
                                if (match.includes("-")) {
                                    return `<span style="color: green;">${match}</span>`;
                                } else {
                                    return `<span style="color: red;">${match}</span>`;
                                }
                            });
                        }

                        const highlightedMarketData = highlightPercentages(marketData);
                        const highlightedAnalysisResult = highlightPercentages(analysisResult);

                        const outputHtml = `<strong>${highlightedMarketData}<br><br></strong>${highlightedAnalysisResult}`;

                        $("#output").html(outputHtml);

                        displaySheets(response.sheets);

                        $("#download_excel")
                            .removeClass("btn-secondary disabled")
                            .addClass("btn-primary")
                            .prop("disabled", false);
                    }
                    else if (response.status === "lose") {
                        $("#output").text(`警告: ${response.message}`);
                    }
                    else if (response.status === "time_fail") {
                        $("#output").text(`警告: ${response.message}`);
                    }
                    else {
                        $("#output").text(`错误: ${response.message}`);
                    }

                });
            });

            $("#download_excel").click(function () {
                if (!$(this).hasClass("disabled")) {
                    window.location.href = "/download";
                }
            });

            function displaySheets(sheets) {
                const sheetTabs = $("#sheetTabs");
                const sheetContent = $("#sheetContent");
                const downloadButton = $("#download_excel");
                const sheetHeader = $("#sheetHeader");

                sheetTabs.empty();
                sheetContent.empty();

                let isActive = true;
                let hasData = false;

                const dimensionPriority = ["子品牌", "省份", "零售商", "商品", "券机制", "券门槛", "优惠力度"];
                const gmvPriority = ["GMV基准值", "消耗基准值", "ROI基准值", "GMV当前值", "活动GMV当前值", "GMV变化值", "活动GMV变化值", "GMV变化率", "活动GMV变化率", "GMV贡献度", "活动GMV贡献度", "消耗当前值", "消耗变化值", "消耗变化率", "消耗贡献度", "ROI当前值", "ROI变化值", "ROI变化率"];

                for (const [sheetName, data] of Object.entries(sheets)) {
                    const tabId = `sheet-${sheetName}`;
                    const tab = $(`
                        <li class="nav-item" role="presentation">
                            <button class="nav-link ${isActive ? "active" : ""}" id="${tabId}-tab" data-bs-toggle="tab" data-bs-target="#${tabId}" type="button" role="tab">
                                ${sheetName}
                            </button>
                        </li>
                    `);
                    sheetTabs.append(tab);

                    const table = $(`<div class="tab-pane fade ${isActive ? "show active" : ""}" id="${tabId}" role="tabpanel"></div>`);
                    if (data.length > 0) {
                        hasData = true;

                        const tableContent = $("<table class='table table-striped'></table>");
                        const thead = $("<thead><tr></tr></thead>");
                        const tbody = $("<tbody></tbody>");

                        const columns = [
                            ...dimensionPriority.filter(col => col in data[0]),
                            ...gmvPriority.filter(col => col in data[0]),
                            ...Object.keys(data[0]).filter(col => !dimensionPriority.includes(col) && !gmvPriority.includes(col))
                        ];

                        columns.forEach((key, index) => {
                            const th = $(`
                                <th>
                                    ${key}
                                    <span class="sort-arrows" style="display: inline-block; margin-left: 0px;">
                                        <span class="arrow-up" style="cursor: pointer; display: block; text-align: center;">▲</span>
                                        <span class="arrow-down" style="cursor: pointer; display: block; text-align: center;">▼</span>
                                    </span>
                                </th>
                            `);

                            th.find(".arrow-up").on("click", () => sortTable(tbody, index, key, true));
                            th.find(".arrow-down").on("click", () => sortTable(tbody, index, key, false));
                            thead.find("tr").append(th);
                        });

                        data.forEach(row => {
                            const tr = $("<tr></tr>");
                            const gmvCurrentValue = row["活动GMV当前值"]
                            const shouldBeGray = gmvCurrentValue < 200;

                            // 优先使用变化率字段，绿涨红跌
                            const gmvChangeRate = row["GMV变化率"]
                                ? parseFloat(row["GMV变化率"].replace(/[%,+]/g, ''))
                                : row["活动GMV变化率"]
                                ? parseFloat(row["活动GMV变化率"].replace(/[%,+]/g, ''))
                                : parseFloat(row["GMV贡献度"]?.replace(/[%,+]/g, '') || row["活动GMV贡献度"]?.replace(/[%,+]/g, '') || '0');

                            let textColor = "";
                            if (shouldBeGray) {
                                textColor = "grey";  // 灰色
                            } else
                            if (!isNaN(gmvChangeRate)) {
                                if (gmvChangeRate === 0) {
                                    textColor = "#333"; // 变化率为0时显示黑色
                                } else {
                                    textColor = gmvChangeRate > 0 ? "#52c41a" : "#f5222d"; // 绿涨红跌
                                }
                            }

                            columns.forEach(key => {
                                const cell = $(`<td>${row[key]}</td>`);
                                if (textColor) {
                                    cell.css("color", textColor);
                                }
                                tr.append(cell);
                            });

                            tbody.append(tr);
                        });

                        tableContent.append(thead, tbody);
                        table.append(tableContent);
                    } else {
                        table.append("<p>数据为空</p>");
                    }

                    sheetContent.append(table);
                    isActive = false;
                }

                if (hasData) {
                    sheetHeader.css("display", "flex");
                    downloadButton.show();
                } else {
                    sheetHeader.css("display", "none");
                    downloadButton.hide();
                }
            }

            function sortTable(tbody, columnIndex, key, ascending) {
                const rows = tbody.find("tr").toArray();

                rows.sort((a, b) => {
                    const aValue = a.cells[columnIndex]?.textContent.trim() || "";
                    const bValue = b.cells[columnIndex]?.textContent.trim() || "";

                    const aNum = parseFloat(aValue.replace('%', ''));
                    const bNum = parseFloat(bValue.replace('%', ''));
                    const isNumeric = !isNaN(aNum) && !isNaN(bNum);

                    if (isNumeric) {
                        return ascending ? aNum - bNum : bNum - aNum;
                    } else {
                        return ascending
                            ? aValue.localeCompare(bValue, undefined, { numeric: true })
                            : bValue.localeCompare(aValue, undefined, { numeric: true });
                    }
                });

                tbody.empty().append(rows);
            }

            $(document).ready(function() {
                // 获取今天的日期
                const today = new Date();

                // Flatpickr 的通用配置
                const commonConfig = {
                    locale: "zh",
                    dateFormat: "Y-m-d",
                    maxDate: new Date(new Date().setDate(new Date().getDate() - 1)),
                    disableMobile: true,
                    allowInput: true
                };

                // 初始化单个日期选择器
                flatpickr("#tar_date", {
                    ...commonConfig,
                    defaultDate: null
                });

                // 初始化日期范围选择器
                const rangePickers = ["#tar_start_date", "#tar_end_date", "#base_start_date", "#base_end_date"];
                rangePickers.forEach(picker => {
                    flatpickr(picker, {
                        ...commonConfig,
                        defaultDate: null,
                        onChange: function(selectedDates, dateStr, instance) {
                            if (picker.includes("tar_")) {
                                updateDateRangeInfo("#tar_start_date", "#tar_end_date", "#tar_date_info");
                            } else {
                                updateDateRangeInfo("#base_start_date", "#base_end_date", "#base_date_info");
                            }
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>