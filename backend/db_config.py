# 定义数据库表名常量
DATABASE_TABLES = {
    # 基础销售数据表
    'SALES': 'dws_o2o_sales_d',
    # 活动明细表
    'ACTIVITY': 'dm_o2o_activity_detail_da',
    # 活动销售分析表（用于计算活动GMV和去重活动GMV）
    'ACTIVITY_ANALYSIS': 'dws_o2o_sale_activity_detail_analysis_d',
    'xx_sales': 'dws_o2o_sales_d_kenvue_2025_sandbox',
    'xx_activity': 'dm_o2o_activity_detail_da_2025_sandbox',
    'xx_activity_analysis': 'dws_o2o_sale_activity_detail_analysis_d_kenvue_2025_sandbox'
}

def get_gmv_table(brand):
    if brand == "xx品牌":
        return DATABASE_TABLES['xx_sales'] 
    else:
        return DATABASE_TABLES['SALES'] 

# 获取活动表名
def get_activity_table(brand):
    """
    获取活动数据表名

    Returns:
        str: 活动数据表名
    """
    if brand == "xx品牌":
        return DATABASE_TABLES['xx_activity']
    else:
        return DATABASE_TABLES['ACTIVITY']

# 获取活动分析表名（用于营销向指标归因分析）
def get_activity_analysis_table(brand):
    """
    获取活动销售分析表名，用于计算活动GMV和去重活动GMV

    Returns:
        str: 活动销售分析表名
    """
    if brand == "xx品牌":
        return DATABASE_TABLES['xx_activity_analysis']
    else:
        return DATABASE_TABLES['ACTIVITY_ANALYSIS']