import { useState, useEffect, useRef, useCallback } from 'react';
import { Container, Form, Button, ProgressBar } from 'react-bootstrap';
import { Select, message, Button as AntButton, DatePicker, Space, Card, Modal, Checkbox, Tabs } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import ResultTable, { SupplySideTable, MarketingSideTable } from './components/ResultTable';
import ProgressBarIsolated from './components/ProgressBarIsolated';
import API from './utils/api';
import { formatDateToYYYYMMDD, calculateDateRange } from './utils/helpers';
import moment from 'moment';
import 'moment/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
// 引入echarts相关组件
import ReactECharts from 'echarts-for-react';

// 供给向指标树状图组件

// 添加格式化AI归因输出的函数
const formatAttributionOutput = (text) => {
  if (!text) return '';
  
  let formattedText = text;

  // 将只有一个#且前后没有#的标题，替换为###
  formattedText = formattedText.replace(/(^|\n)# ([^#\n]+)(?=\n|$)/g, '$1### $2');
  
  // 完全移除所有分隔线（---）
  formattedText = formattedText.replace(/---+\n*/g, '');
  
  // 将**文字**改为加粗但不改变颜色
  formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<span style="font-weight: bold;">$1</span>');
  
  // 处理一级标题（单个#）
  formattedText = formattedText.replace(/^# (.*?)(?:\n|$)/gm, '<h1 style="font-size: 24px; font-weight: bold; margin-top: 30px; margin-bottom: 20px;">$1</h1>');
  
  // 处理二级标题（两个##）
  formattedText = formattedText.replace(/^## (.*?)(?:\n|$)/gm, '<h2 style="font-size: 20px; font-weight: bold; margin-top: 25px; margin-bottom: 15px;">$1</h2>');
  
  // 处理三级标题（三个###）
  formattedText = formattedText.replace(/^### (.*?)(?:\n|$)/gm, '<h3 style="font-size: 18px; font-weight: bold; margin-top: 20px; margin-bottom: 10px;">$1</h3>');
  
  // 处理四级标题（####），兼容多空格和**包裹，且不要求前面必须是换行
  formattedText = formattedText.replace(/(^|[^#])####\s*\*{0,2}\s*([^*\n]+?)\s*\*{0,2}(?=\n|$)/g, (_, pre, title) => {
    const cleanTitle = title.replace(/^\d+\.\s*/, '').trim();
    return `${pre}<h4 style="font-size: 16px; font-weight: bold; margin-top: 15px; margin-bottom: 8px; padding-left: 20px; border-left: 3px solid #1890ff;">${cleanTitle}</h4>`;
  });

  // 处理五级标题（#####），使用与四级标题相同的样式
  formattedText = formattedText.replace(/(^|[^#])#####\s*\*{0,2}\s*([^*\n]+?)\s*\*{0,2}(?=\n|$)/g, (_, pre, title) => {
    const cleanTitle = title.replace(/^\d+\.\s*/, '').trim();
    return `${pre}<h4 style="font-size: 16px; font-weight: bold; margin-top: 15px; margin-bottom: 8px; padding-left: 20px; border-left: 3px solid #1890ff;">${cleanTitle}</h4>`;
  });
  
  // 移除所有行首多余的#号（包括#、##、###、####等）
  formattedText = formattedText.replace(/^#+\s*/gm, '');
  
  // 为带正负号的数字添加颜色（确保百分比符号被完整捕获）
  formattedText = formattedText.replace(/([+-][0-9.,]+%)/g, (match) => {
    const color = match.startsWith('+') ? '#52c41a' : '#f5222d';
    return `<span style="color: ${color}; font-weight: bold;">${match}</span>`;
  });
  
  

  
  return formattedText;
};

// 维度聚合管理器类
class DrillDownManager {
  constructor() {
    this.dimensionApiMap = {
      '平台': 'platform',
      '省份': 'province', 
      '城市': 'standard_city',  // 根据SQL示例，城市字段应该是standard_city
      '零售商': 'retailer',
      '子品牌': 'sub_brand',
      '商品名称': 'search_text',
      '商品': 'product_name',  // 修改商品维度映射为product_name
      '券机制': 'coupon_mechanism',
      '券门槛': 'coupon_threshold',
      '优惠力度': 'coupon_discount'
    };
    
    // 添加反向映射和别名支持
    this.fieldAliases = {
      'city': 'standard_city',  // 如果交叉维度数据中字段名是city
      'standard_city': 'standard_city',
      'platform': 'platform',
      'province': 'province',
      'retailer': 'retailer',
      'sub_brand': 'sub_brand',
      'vender_name': 'retailer',  // 零售商可能使用vender_name
      'search_text': 'search_text',
      'product_name': 'product_name',
      'coupon_mechanism': 'coupon_mechanism',
      'coupon_threshold': 'coupon_threshold',
      'coupon_discount': 'coupon_discount'
    };
  }

  // 创建下钻路径项
  createPathItem(dimension, value, level = 0, upc = null, crossDimensionData = null) {
    const pathItem = {
      dimension,
      value,
      level,
      apiKey: this.dimensionApiMap[dimension],
      displayName: `${dimension}: ${value}`
    };
    
    // 如果是商品维度，添加UPC信息
    if ((dimension === '商品名称' || dimension === '商品') && upc) {
      pathItem.upc = upc;
      pathItem.displayName = `${dimension}: ${value} (UPC: ${upc})`;
    }
    
    // 如果是交叉维度，保存交叉维度数据
    if (crossDimensionData) {
      pathItem.crossDimensionData = crossDimensionData;
    }
    
    return pathItem;
  }

  // 构建筛选条件对象
  buildFilters(drillDownPath) {
    const filters = {};
    drillDownPath.forEach(pathItem => {
      if (pathItem.apiKey) {
        filters[pathItem.apiKey] = pathItem.value;
        
        // 如果是商品维度且有UPC信息，也添加UPC筛选条件
        if ((pathItem.dimension === '商品名称' || pathItem.dimension === '商品') && pathItem.upc) {
          filters['upc'] = pathItem.upc;
        }
      }
    });
    return filters;
  }

  // 为交叉维度构建筛选条件，包含交叉维度本身的筛选
  buildFiltersForCrossDimension(drillDownPath, crossDimensionData) {
    const filters = this.buildFilters(drillDownPath);
    
    // 如果有交叉维度数据，需要添加交叉维度的筛选条件
    if (crossDimensionData && typeof crossDimensionData === 'object') {
      // 从交叉维度数据中提取维度值，并转换为API参数
      Object.keys(crossDimensionData).forEach(key => {
        let apiKey = this.dimensionApiMap[key];
        
        // 如果直接映射没找到，尝试使用别名映射
        if (!apiKey && this.fieldAliases[key]) {
          apiKey = this.fieldAliases[key];
        }
        
        // 如果key本身就是API字段名，直接使用
        if (!apiKey && this.fieldAliases[key.toLowerCase()]) {
          apiKey = this.fieldAliases[key.toLowerCase()];
        }
        
        if (apiKey && crossDimensionData[key]) {
          filters[apiKey] = crossDimensionData[key];
        }
      });
    }
    
    return filters;
  }

  // 获取下钻路径的显示文本
  getPathDisplayText(drillDownPath) {
    return drillDownPath.map(item => item.displayName).join(' > ');
  }

  // 添加下钻级别
  addDrillLevel(currentPath, dimension, value, upc = null) {
    const newLevel = currentPath.length;
    const newPathItem = this.createPathItem(dimension, value, newLevel, upc);
    return [...currentPath, newPathItem];
  }

  // 移除指定级别及之后的下钻
  removeDrillLevel(currentPath, targetLevel) {
    return currentPath.filter(item => item.level < targetLevel);
  }

  // 获取当前可下钻的维度
  getAvailableDrillDimensions(selectedAnalysisDimensions, currentPath) {
    if (!selectedAnalysisDimensions || selectedAnalysisDimensions.length === 0) {
      return [];
    }

    // 获取已经下钻的维度
    const drilledDimensions = currentPath.map(item => item.dimension);
    
    // 返回还未下钻的维度
    return selectedAnalysisDimensions.filter(dim => !drilledDimensions.includes(dim));
  }

  // 获取下一个可下钻的维度（按照维度顺序）
  getNextDrillDimension(selectedAnalysisDimensions, currentPath) {
    const availableDimensions = this.getAvailableDrillDimensions(selectedAnalysisDimensions, currentPath);
    return availableDimensions.length > 0 ? availableDimensions[0] : null;
  }

  // 检查是否可以继续下钻
  canDrillDown(selectedAnalysisDimensions, currentPath) {
    return this.getAvailableDrillDimensions(selectedAnalysisDimensions, currentPath).length > 0;
  }

  // 获取当前下钻级别
  getCurrentDrillLevel(drillDownPath) {
    return drillDownPath.length;
  }

  // 构建多级下钻的缓存键（支持嵌套下钻）
  generateNestedCacheKey(drillDownPath, targetDimension) {
    const pathString = drillDownPath.map(item => `${item.dimension}:${item.value}`).join('|');
    return `${pathString}_${targetDimension}`;
  }

  // 生成下钻数据的缓存键
  generateCacheKey(pathItem, targetDimension) {
    return `${pathItem.dimension}_${pathItem.value}_${targetDimension}`;
  }

  // 获取当前下钻级别的目标维度
  getCurrentTargetDimension(drillDownPath) {
    return drillDownPath.length > 0 
      ? drillDownPath[drillDownPath.length - 1].dimension 
      : null;
  }

  // 构建面包屑导航数据
  buildBreadcrumbData(drillDownPath, selectedAnalysisDimensions) {
    const breadcrumbs = [];
    
    // 添加根级别
    if (selectedAnalysisDimensions && selectedAnalysisDimensions.length > 0) {
      breadcrumbs.push({
        title: selectedAnalysisDimensions[0],
        level: -1,
        isRoot: true
      });
    }

    // 添加下钻路径
    drillDownPath.forEach((pathItem, index) => {
      breadcrumbs.push({
        title: pathItem.displayName,
        level: index,
        pathItem,
        isActive: index === drillDownPath.length - 1
      });
    });

    return breadcrumbs;
  }
}

// 创建全局的下钻管理器实例
const drillDownManager = new DrillDownManager();

function App() {
  // 从URL获取brand参数
  const urlParams = new URLSearchParams(window.location.search);
  const brandFromUrl = urlParams.get('brand');

  // 状态变量
  const [dateType, setDateType] = useState('single');
  const [tarDate, setTarDate] = useState(null);
  const [tarStartDate, setTarStartDate] = useState(null);
  const [tarEndDate, setTarEndDate] = useState(null);
  const [baseStartDate, setBaseStartDate] = useState(null);
  const [baseEndDate, setBaseEndDate] = useState(null);
  const [attrIndex, setAttrIndex] = useState('全量GMV');

  // 筛选维度状态 - 改为数组以支持多选
  const [subBrands, setSubBrands] = useState(['全部']);
  const [provinces, setProvinces] = useState(['全部']);
  const [retailers, setRetailers] = useState(['全部']);
  const [subBrand, setSubBrand] = useState(['全部']);
  const [province, setProvince] = useState(['全部']);
  const [retailer, setRetailer] = useState(['全部']);

  // 新增平台和UPC/商品名称搜索框状态
  const [platforms, setPlatforms] = useState(['全部']);
  const [platform, setPlatform] = useState(['全部']);
  const [products, setProducts] = useState([{product_name: '全部', upc: ''}]);
  const [selectedProduct, setSelectedProduct] = useState(['全部']);
  
  // 筛选下拉框显示状态
  const [platformDropdownVisible, setPlatformDropdownVisible] = useState(false);
  const [provinceDropdownVisible, setProvinceDropdownVisible] = useState(false);
  const [retailerDropdownVisible, setRetailerDropdownVisible] = useState(false);
  const [subBrandDropdownVisible, setSubBrandDropdownVisible] = useState(false);
  const [productDropdownVisible, setProductDropdownVisible] = useState(false);
  const [couponMechanismDropdownVisible, setCouponMechanismDropdownVisible] = useState(false);
  const [couponThresholdDropdownVisible, setCouponThresholdDropdownVisible] = useState(false);
  const [couponDiscountDropdownVisible, setCouponDiscountDropdownVisible] = useState(false);
  
  // 创建引用以便在点击外部时关闭下拉框
  const platformRef = useRef(null);
  const provinceRef = useRef(null);
  const retailerRef = useRef(null);
  const subBrandRef = useRef(null);
  const productRef = useRef(null);
  const couponMechanismRef = useRef(null);
  const couponThresholdRef = useRef(null);
  const couponDiscountRef = useRef(null);
  
  // 券相关状态
  const [couponMechanisms, setCouponMechanisms] = useState(['全部']);
  const [couponThresholds, setCouponThresholds] = useState(['全部']);
  const [couponDiscounts, setCouponDiscounts] = useState(['全部']);
  const [selectedCouponMechanism, setSelectedCouponMechanism] = useState(['全部']);
  const [selectedCouponThreshold, setSelectedCouponThreshold] = useState(['全部']);
  const [selectedCouponDiscount, setSelectedCouponDiscount] = useState(['全部']);
  
  // 分析结果状态
  const [showCouponFilters, setShowCouponFilters] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [output, setOutput] = useState('');
  const [sheets, setSheets] = useState({});
  const [marketData, setMarketData] = useState('');

  // 加载状态
  const [platformsLoading, setPlatformsLoading] = useState(false);
  const [provincesLoading, setProvincesLoading] = useState(false);
  const [retailersLoading, setRetailersLoading] = useState(false);
  const [subBrandsLoading, setSubBrandsLoading] = useState(false);
  const [productsLoading, setProductsLoading] = useState(false);
  const [couponMechanismsLoading, setCouponMechanismsLoading] = useState(false);
  const [couponThresholdsLoading, setCouponThresholdsLoading] = useState(false);
  const [couponDiscountsLoading, setCouponDiscountsLoading] = useState(false);

  // 添加获取红黑榜TOP3状态和处理函数
  const [isLoadingTopRanking, setIsLoadingTopRanking] = useState(false);
  // 添加TOP3数据和表格数据的状态
  const [sheetsData, setSheetsData] = useState({});
  // 添加市场数据状态
  const [rankingMarketData, setRankingMarketData] = useState(null);

  // 添加状态变量控制是否显示归因分析功能
  const [showAttributionAnalysis, setShowAttributionAnalysis] = useState(false);

  // 添加历史GMV变化数据状态
  const [historicalGMVChanges, setHistoricalGMVChanges] = useState([]);

  // 添加供给向指标数据状态
  const [supplySideData, setSupplySideData] = useState(null);
  const [analysisType, setAnalysisType] = useState(null);
  
  // 添加营销向指标数据状态
  const [marketingSideData, setMarketingSideData] = useState(null);

  // 添加当前活动标签页状态
  const [activeTab, setActiveTab] = useState('overall');

  // 添加城市相关状态变量
  const [cities, setCities] = useState(['全部']);
  const [city, setCity] = useState(['全部']);
  const [cityDropdownVisible, setCityDropdownVisible] = useState(false);
  const [citiesLoading, setCitiesLoading] = useState(false);

  // 添加城市下拉框引用 - 在其他筛选维度引用附近添加
  const cityRef = useRef(null);

  // 在其他state声明下面添加
  const [filePath, setFilePath] = useState(null);

  // 新增时间对比弹框相关状态
  const [timeCompareModalVisible, setTimeCompareModalVisible] = useState(false);
  const [modalDateType, setModalDateType] = useState('日');
  const [modalTargetDateType, setModalTargetDateType] = useState('指定日期');
  const [modalCompareMethod, setModalCompareMethod] = useState('同比');
  const [modalCompareDetailType, setModalCompareDetailType] = useState('日同比');
  const [modalStartDateType, setModalStartDateType] = useState('指定日期');

  // 在state变量中添加筛选条件选择状态
  const [selectedFilterConditions, setSelectedFilterConditions] = useState([]);

  // 新增归因设置弹框相关状态
  const [attributionModalVisible, setAttributionModalVisible] = useState(false);
  const [attributionMethods, setAttributionMethods] = useState([]);
  const [activeAttributionTab, setActiveAttributionTab] = useState('维度');
  const [selectedAnalysisDimensions, setSelectedAnalysisDimensions] = useState([]);
  
  // 交叉维度归因相关状态
  const [crossDimensionEnabled, setCrossDimensionEnabled] = useState(false);
  const [dimensionCombinations, setDimensionCombinations] = useState([{ id: 1, dimensions: [] }]);
  
  // 指标归因相关状态
  const [indicatorType, setIndicatorType] = useState([]);



  // 新增归因分析相关状态
  const [currentAttributionTab, setCurrentAttributionTab] = useState('维度归因'); // 当前归因分析主标签页
  const [currentDimensionType, setCurrentDimensionType] = useState('单一维度'); // 单一维度或交叉维度
  const [currentDimensionTab, setCurrentDimensionTab] = useState(''); // 当前维度标签页
  const [currentCrossTab, setCurrentCrossTab] = useState('组合1'); // 当前交叉维度标签页
  const [currentIndicatorTab, setCurrentIndicatorTab] = useState(''); // 当前指标类型标签页

  // 新增下钻功能相关状态
  const [drillDownPath, setDrillDownPath] = useState([]); // 下钻路径
  const [drillDownData, setDrillDownData] = useState({}); // 下钻数据缓存
  const [expandedRows, setExpandedRows] = useState(new Set()); // 跟踪展开的行

  // 新增：保存一键归因时的设置状态，用于锁定归因分析界面
  const [lockedAttributionSettings, setLockedAttributionSettings] = useState({
    attributionMethods: [],
    selectedAnalysisDimensions: [],
    crossDimensionEnabled: false,
    dimensionCombinations: [],
    indicatorType: []
  });

  // 重置所有筛选维度数据的函数
  const resetAllFilters = () => {
    // 重置普通筛选维度
    setSubBrands(['全部']);
    setProvinces(['全部']);
    setRetailers(['全部']);
    setPlatforms(['全部']);
    setCities(['全部']);
    setProducts([{product_name: '全部', upc: ''}]);
    setSelectedProduct(['全部']);
    
    // 重置筛选值为数组
    setPlatform(['全部']);
    setProvince(['全部']);
    setRetailer(['全部']);
    setSubBrand(['全部']);
    setCity(['全部']);
    
    // 重置券相关筛选维度
    setCouponMechanisms(['全部']);
    setCouponThresholds(['全部']);
    setCouponDiscounts(['全部']);
    setSelectedCouponMechanism(['全部']);
    setSelectedCouponThreshold(['全部']);
    setSelectedCouponDiscount(['全部']);
    
    // 重置下拉框打开状态，确保下次点击时能重新调用接口
    setSubBrandDropdownVisible(false);
    setProvinceDropdownVisible(false);
    setRetailerDropdownVisible(false);
    setPlatformDropdownVisible(false);
    setCityDropdownVisible(false);
    setProductDropdownVisible(false);
    setCouponMechanismDropdownVisible(false);
    setCouponThresholdDropdownVisible(false);
    setCouponDiscountDropdownVisible(false);
  };

  // 监听日期类型变化
  useEffect(() => {
    // 移除自动重置归因指标的逻辑，让用户自主选择
    // if (attrIndex === '活动GMV') {
    //   setAttrIndex('全量GMV');
    //   setShowCouponFilters(false);
    // }

    if (dateType === 'range') {
      // 清空单日期
      setTarDate(null);
    } else {
      // 清空日期范围
      setTarStartDate(null);
      setTarEndDate(null);
      setBaseStartDate(null);
      setBaseEndDate(null);
    }
    
    // 重置所有筛选维度数据
    resetAllFilters();
    
    // 重置所有结果数据
    setHistoricalGMVChanges([]);
    setRankingMarketData(null);
    setSheetsData({});
    setSheets({});
    setOutput('');
    setMarketData('');
    setSupplySideData(null);
    setMarketingSideData(null);
    setAnalysisType(null);
    setShowAttributionAnalysis(false);
    setActiveTab('overall');
  }, [dateType]);

  // 监听目标日期范围变化
  useEffect(() => {
    if (dateType === 'range' && tarStartDate && tarEndDate) {
      const result = calculateDateRange(tarStartDate, tarEndDate);
      if (!result.valid) {
        message.error(result.message);
        setTarEndDate(null);
      } else {
        // 如果日期范围更改，重置筛选维度
        if (baseStartDate && baseEndDate) {
          resetAllFilters();
        }
      }
    }
  }, [tarStartDate, tarEndDate, dateType, baseStartDate, baseEndDate]);

  // 监听对比日期范围变化
  useEffect(() => {
    if (dateType === 'range' && baseStartDate && baseEndDate) {
      const result = calculateDateRange(baseStartDate, baseEndDate);
      if (!result.valid) {
        message.error(result.message);
        setBaseEndDate(null);
      } else {
        // 如果日期范围更改，重置筛选维度
        if (tarStartDate && tarEndDate) {
          resetAllFilters();
        }
      }
    }
  }, [baseStartDate, baseEndDate, dateType, tarStartDate, tarEndDate]);

  // 监听单日期变化
  useEffect(() => {
    if (dateType === 'single' && tarDate) {
      // 单日期更改时重置筛选维度
      resetAllFilters();
    }
  }, [tarDate, dateType]);

  // 统一的日期格式化函数，确保所有地方使用相同的格式化逻辑
  const formatMomentToYYYYMMDD = (momentObj) => {
    if (!momentObj) return null;
    if (momentObj._isAMomentObject) {
      return momentObj.format('YYYYMMDD');
    }
    return formatDateToYYYYMMDD(momentObj);
  };

  // 获取日期数据 - 参考 fetchTopRanking 中的日期处理逻辑
  const getDateData = () => {
    const data = {};

    if (dateType === 'single') {
      if (modalDateType === '月' || modalDateType === '年') {
        // 月维度和年维度：转换为日期范围模式
        let tarStartFormatted, tarEndFormatted, baseStartFormatted, baseEndFormatted;

        if (modalDateType === '月') {
          // 月维度：传递整个月的范围
          tarStartFormatted = formatMomentToYYYYMMDD(tarDate.clone().startOf('month'));
          tarEndFormatted = formatMomentToYYYYMMDD(tarDate.clone().endOf('month'));
          if (baseStartDate) {
            baseStartFormatted = formatMomentToYYYYMMDD(baseStartDate.clone().startOf('month'));
            baseEndFormatted = formatMomentToYYYYMMDD(baseStartDate.clone().endOf('month'));
          }
        } else if (modalDateType === '年') {
          // 年维度：传递整年的范围
          tarStartFormatted = formatMomentToYYYYMMDD(tarDate.clone().startOf('year'));
          tarEndFormatted = formatMomentToYYYYMMDD(tarDate.clone().endOf('year'));
          if (baseStartDate) {
            baseStartFormatted = formatMomentToYYYYMMDD(baseStartDate.clone().startOf('year'));
            baseEndFormatted = formatMomentToYYYYMMDD(baseStartDate.clone().endOf('year'));
          }
        }

        data.tar_start_date = tarStartFormatted;
        data.tar_end_date = tarEndFormatted;
        if (baseStartFormatted && baseEndFormatted) {
          data.base_start_date = baseStartFormatted;
          data.base_end_date = baseEndFormatted;
        }
      } else {
        // 日维度：传递具体日期
        data.tar_date = formatMomentToYYYYMMDD(tarDate);
        if (baseStartDate) {
          data.base_date = formatMomentToYYYYMMDD(baseStartDate);
        }
      }
    } else {
      // 日期范围模式：传递目标日期范围和对比日期范围
      data.tar_start_date = formatMomentToYYYYMMDD(tarStartDate);
      data.tar_end_date = formatMomentToYYYYMMDD(tarEndDate);
      data.base_start_date = formatMomentToYYYYMMDD(baseStartDate);
      data.base_end_date = formatMomentToYYYYMMDD(baseEndDate);
    }

    return data;
  };

  // 新增：创建日期参数对象，用于传递给子组件
  const getDateParams = () => {
    return {
      // 当前日期选择类型：single 或 range
      dateType,
      // 对于"指定日期"模式保存目标/对比日期
      tarDate,
      // 为兼容旧逻辑，单日期模式下的对比日期仍然使用 baseStartDate 字段
      baseDate: baseStartDate,
      baseStartDate,
      // 日期范围模式字段
      tarStartDate,
      tarEndDate,
      baseEndDate,
      // 传递用户在时间对比弹窗里选择的日期维度（日/月/年）
      modalDateType
    };
  };

  // 重置相关筛选条件

  // 禁用今天和未来日期

  // 按需获取子品牌数据
  const fetchSubBrands = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setSubBrandsLoading(true);
    try {
      // 添加其他维度参数
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        province: Array.isArray(province) ? province.join(',') : province,
        vender: Array.isArray(retailer) ? retailer.join(',') : retailer,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        attr_index: attrIndex
      };
      const subBrandList = await API.getSubBrands(params);
      setSubBrands(['全部', ...subBrandList]);
    } catch (error) {
      message.error('获取子品牌数据失败');
    } finally {
      setSubBrandsLoading(false);
    }
  };

  // 按需获取省份数据
  const fetchProvinces = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setProvincesLoading(true);
    try {
      // 添加其他维度参数
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        vender: Array.isArray(retailer) ? retailer.join(',') : retailer,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        attr_index: attrIndex
      };
      const provinceList = await API.getProvinces(params);
      setProvinces(['全部', ...provinceList]);
    } catch (error) {
      console.error('获取省份数据失败', error);
    } finally {
      setProvincesLoading(false);
    }
  };

  // 按需获取零售商数据
  const fetchRetailers = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setRetailersLoading(true);
    try {
      // 添加其他维度参数
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        province: Array.isArray(province) ? province.join(',') : province,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        attr_index: attrIndex
      };
      const retailerList = await API.getRetailers(params);
      setRetailers(['全部', ...retailerList]);
    } catch (error) {
      message.error('获取零售商数据失败');
    } finally {
      setRetailersLoading(false);
    }
  };

  // 新增：按需获取平台数据
  const fetchPlatforms = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setPlatformsLoading(true);
    try {
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        province: Array.isArray(province) ? province.join(',') : province,
        vender: Array.isArray(retailer) ? retailer.join(',') : retailer,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        attr_index: attrIndex
      };
      // 假设API中已添加获取平台列表的方法
      const platformList = await API.getPlatforms(params);
      setPlatforms(['全部', ...platformList]);
    } catch (error) {
      message.error('获取平台数据失败');
    } finally {
      setPlatformsLoading(false);
    }
  };

  // 按需获取券机制数据
  const fetchCouponMechanisms = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setCouponMechanismsLoading(true);
    try {
      // 添加其他维度参数
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        province: Array.isArray(province) ? province.join(',') : province,
        vender: Array.isArray(retailer) ? retailer.join(',') : retailer,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        attr_index: attrIndex
      };
      const mechanisms = await API.getCouponMechanisms(params);
      setCouponMechanisms(['全部', ...mechanisms]);
    } catch (error) {
      message.error('获取券机制数据失败');
    } finally {
      setCouponMechanismsLoading(false);
    }
  };

  // 按需获取券门槛数据
  const fetchCouponThresholds = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setCouponThresholdsLoading(true);
    try {
      // 添加其他维度参数
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        province: Array.isArray(province) ? province.join(',') : province,
        vender: Array.isArray(retailer) ? retailer.join(',') : retailer,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        attr_index: attrIndex
      };
      const thresholds = await API.getCouponThreshold(params);
      setCouponThresholds(['全部', ...thresholds]);
    } catch (error) {
      message.error('获取券门槛数据失败');
    } finally {
      setCouponThresholdsLoading(false);
    }
  };

  // 按需获取优惠力度数据
  const fetchCouponDiscounts = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setCouponDiscountsLoading(true);
    try {
      // 添加其他维度参数
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        province: Array.isArray(province) ? province.join(',') : province,
        vender: Array.isArray(retailer) ? retailer.join(',') : retailer,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        attr_index: attrIndex
      };
      const discounts = await API.getCouponDiscount(params);
      setCouponDiscounts(['全部', ...discounts]);
    } catch (error) {
      message.error('获取优惠力度数据失败');
    } finally {
      setCouponDiscountsLoading(false);
    }
  };

  // 添加获取城市数据的函数
  const fetchCities = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setCitiesLoading(true);
    try {
      // 添加其他维度参数
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        province: Array.isArray(province) ? province.join(',') : province,
        vender: Array.isArray(retailer) ? retailer.join(',') : retailer,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        attr_index: attrIndex
      };
      const cityList = await API.getCities(params);
      setCities(['全部', ...cityList]);
    } catch (error) {
      message.error('获取城市数据失败');
    } finally {
      setCitiesLoading(false);
    }
  };

  // 新增：按需获取UPC/商品名称数据
  const fetchProducts = async () => {
    const dateData = getDateData();
    if (Object.keys(dateData).length === 0) {
      message.warning('请先选择完整的日期！');
      return;
    }

    setProductsLoading(true);
    try {
      const params = {
        ...dateData,
        brand: brandFromUrl, // 添加品牌参数
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        province: Array.isArray(province) ? province.join(',') : province,
        vender: Array.isArray(retailer) ? retailer.join(',') : retailer,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        attr_index: attrIndex
      };
      // API现在返回对象数组，每个对象包含product_name和upc
      const productList = await API.getProducts(params);
      setProducts([{product_name: '全部', upc: ''}, ...productList]);
    } catch (error) {
      message.error('获取UPC/商品名称数据失败');
    } finally {
      setProductsLoading(false);
    }
  };

  // 新增：UPC/商品名称下拉框点击处理 - 需要先选择完整日期
  const handleProductDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setProductDropdownVisible(!productDropdownVisible);
    fetchProducts();
  };

  // 指标归因处理函数
  const handleIndicatorTypeChange = useCallback((values) => {
    let newValues = Array.isArray(values) ? [...values] : [];

    if (attrIndex === '活动GMV') {
      // 活动GMV下禁止供给向
      newValues = newValues.filter(v => v !== '供给向');
    } else {
      // 如果选择了供给向但时间对比不是基于月维度，则过滤掉供给向
      newValues = newValues.filter(v => {
        if (v === '供给向') {
          // 当日期类型为月时，允许所有对比方式（月环比、年同比）
          // 当日期类型为日时，只允许包含"月"的对比方式（月同比、月环比）
          return modalDateType === '月' || (modalCompareDetailType && modalCompareDetailType.includes('月'));
        }
        return true;
      });
    }

    setIndicatorType(newValues);
  }, [attrIndex, modalDateType, modalCompareDetailType]);

  // 监听归因指标变化
  useEffect(() => {
    if (attrIndex === '活动GMV') {
      // 直接显示券相关筛选，不进行日期验证
      setShowCouponFilters(true);
      // 重置券相关筛选维度
      setCouponMechanisms(['全部']);
      setCouponThresholds(['全部']);
      setCouponDiscounts(['全部']);
      
      // 如果当前选择了供给向指标，则清除
      if (indicatorType.includes('供给向')) {
        const newIndicatorType = indicatorType.filter(item => item !== '供给向');
        handleIndicatorTypeChange(newIndicatorType);
      }
    } else {
      setShowCouponFilters(false);
    }
    
    // 重置所有下拉框打开状态，确保下次点击时能重新调用接口
    setSubBrandDropdownVisible(false);
    setProvinceDropdownVisible(false);
    setRetailerDropdownVisible(false);
    setPlatformDropdownVisible(false);
    setCityDropdownVisible(false); // 新增：重置城市下拉框状态
    setProductDropdownVisible(false);
    setCouponMechanismDropdownVisible(false);
    setCouponThresholdDropdownVisible(false);
    setCouponDiscountDropdownVisible(false);
    
  }, [attrIndex, indicatorType, handleIndicatorTypeChange]);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    function handleClickOutside(event) {
      if (platformRef.current && !platformRef.current.contains(event.target)) {
        setPlatformDropdownVisible(false);
      }
      if (provinceRef.current && !provinceRef.current.contains(event.target)) {
        setProvinceDropdownVisible(false);
      }
      if (retailerRef.current && !retailerRef.current.contains(event.target)) {
        setRetailerDropdownVisible(false);
      }
      if (subBrandRef.current && !subBrandRef.current.contains(event.target)) {
        setSubBrandDropdownVisible(false);
      }
      if (cityRef.current && !cityRef.current.contains(event.target)) { // 新增：城市下拉框点击外部关闭
        setCityDropdownVisible(false);
      }
      if (productRef.current && !productRef.current.contains(event.target)) {
        setProductDropdownVisible(false);
      }
      if (couponMechanismRef.current && !couponMechanismRef.current.contains(event.target)) {
        setCouponMechanismDropdownVisible(false);
      }
      if (couponThresholdRef.current && !couponThresholdRef.current.contains(event.target)) {
        setCouponThresholdDropdownVisible(false);
      }
      if (couponDiscountRef.current && !couponDiscountRef.current.contains(event.target)) {
        setCouponDiscountDropdownVisible(false);
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 根据输入过滤选项
  const filterOption = (input, option) => {
    if (!option || !option.children) return false;
    return option.children.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 单日期选择和改变

  // 验证日期是否完整选择
  const validateDatesComplete = () => {
    if (dateType === 'single') {
      // 单日期模式：需要选择目标日期，对比日期可选
      return tarDate !== null;
    } else if (dateType === 'range') {
      // 日期范围模式：需要选择完整的目标日期范围和对比日期范围
      return tarStartDate && tarEndDate && baseStartDate && baseEndDate;
    }
    return false;
  };

  // 筛选框点击处理 - 需要先选择完整日期
  const handleSubBrandDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setSubBrandDropdownVisible(!subBrandDropdownVisible);
    fetchSubBrands();
  };

  const handleProvinceDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setProvinceDropdownVisible(!provinceDropdownVisible);
    fetchProvinces();
  };

  const handleRetailerDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setRetailerDropdownVisible(!retailerDropdownVisible);
    fetchRetailers();
  };

  // 新增：平台下拉框点击处理
  const handlePlatformDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setPlatformDropdownVisible(!platformDropdownVisible);
    fetchPlatforms();
  };

  // 券相关筛选框点击处理 - 需要先选择完整日期
  const handleCouponMechanismDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setCouponMechanismDropdownVisible(!couponMechanismDropdownVisible);
    fetchCouponMechanisms();
  };

  const handleCouponThresholdDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setCouponThresholdDropdownVisible(!couponThresholdDropdownVisible);
    fetchCouponThresholds();
  };

  const handleCouponDiscountDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setCouponDiscountDropdownVisible(!couponDiscountDropdownVisible);
    fetchCouponDiscounts();
  };

  // 筛选值改变处理 - 改为支持多选，自动处理"全部"选项
  const handleSubBrandChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setSubBrand(newValues);
    setSubBrandDropdownVisible(false);
  };

  const handleProvinceChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setProvince(newValues);
    setProvinceDropdownVisible(false);
  };

  const handleRetailerChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setRetailer(newValues);
    setRetailerDropdownVisible(false);
  };

  // 券相关筛选值改变处理 - 改为支持多选，自动处理"全部"选项
  const handleCouponMechanismChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setSelectedCouponMechanism(newValues);
    setCouponMechanismDropdownVisible(false);
  };

  const handleCouponThresholdChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setSelectedCouponThreshold(newValues);
    setCouponThresholdDropdownVisible(false);
  };

  const handleCouponDiscountChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setSelectedCouponDiscount(newValues);
    setCouponDiscountDropdownVisible(false);
  };

  // 新增：搜索框文本变化处理 - 改为支持多选，自动处理"全部"选项
  const handleSearchTextChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setSelectedProduct(newValues);
    setProductDropdownVisible(false);
  };

  // 新增：平台选择变化处理 - 改为支持多选，自动处理"全部"选项
  const handlePlatformChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setPlatform(newValues);
    setPlatformDropdownVisible(false);
  };

  // 更新平台筛选框输入

  // 选择平台

  // 更新省份筛选框输入

  // 选择省份

  // 更新零售商筛选框输入

  // 选择零售商

  // 更新子品牌筛选框输入

  // 选择子品牌

  // 更新券机制筛选框输入

  // 选择券机制

  // 更新券门槛筛选框输入

  // 选择券门槛

  // 更新优惠力度筛选框输入

  // 选择优惠力度

  // 更新产品筛选框输入

  // 选择产品

  // 添加数据处理函数

  // 添加根据商品名称获取UPC的辅助函数
  const getUpcByProductNames = (productNames) => {
    if (!Array.isArray(productNames) || productNames.includes('全部')) {
      return '全部';
    }
    
    const upcs = [];
    productNames.forEach(productName => {
      const product = products.find(p => p.product_name === productName);
      if (product && product.upc) {
        upcs.push(product.upc);
      }
    });
    
    return upcs.length > 0 ? upcs.join(',') : '全部';
  };

  // 修改fetchTopRanking函数，确保AI归因分析被调用
  const fetchTopRanking = async () => {
    // 验证归因方式是否选择
    if (!attributionMethods || attributionMethods.length === 0) {
      message.warning('请先在归因设置中选择归因方式！');
      return;
    }
    
    // 验证日期输入
    if (dateType === 'single' && !tarDate) {
      message.warning('请选择目标日期！');
      return;
    } else if (
      dateType === 'range' &&
      (!tarStartDate || !tarEndDate || !baseStartDate || !baseEndDate)
    ) {
      message.warning('请选择完整的日期范围！');
      return;
    }
    
    setIsLoadingTopRanking(true);
    setShowAttributionAnalysis(false); // 重新获取数据时，隐藏归因分析功能
    
    try {
      // 准备请求数据
      const data = {
        sub_brand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
        province: Array.isArray(province) ? province.join(',') : province,
        retailer: Array.isArray(retailer) ? retailer.join(',') : retailer,
        platform: Array.isArray(platform) ? platform.join(',') : platform,
        city: Array.isArray(city) ? city.join(',') : city,
        search_text: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
        upc: getUpcByProductNames(selectedProduct), // 修改：使用辅助函数获取正确的UPC
        attr_index: attrIndex,
        coupon_mechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
        coupon_threshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
        coupon_discount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount,
        brand: brandFromUrl,
        analysis_dimensions: Array.isArray(selectedAnalysisDimensions) ? selectedAnalysisDimensions.join(',') : '',
        // 添加交叉维度参数
        cross_dimension_enabled: crossDimensionEnabled,
        dimension_combinations: JSON.stringify(dimensionCombinations),
        // 添加日期类型参数
        date_type: modalDateType,
        // 添加对比方式参数
        compare_detail_type: modalCompareDetailType,
        // 添加指标归因相关参数
        attribution_methods: Array.isArray(attributionMethods) ? attributionMethods.join(',') : '',
        indicator_types: Array.isArray(indicatorType) ? indicatorType.join(',') : ''
      };



      // 修改日期参数传递逻辑，使用统一的日期格式化函数
      if (dateType === 'single') {
        if (modalDateType === '月' || modalDateType === '年') {
          // 月维度和年维度：转换为日期范围模式
          let tarStartFormatted, tarEndFormatted, baseStartFormatted, baseEndFormatted;

          if (modalDateType === '月') {
            // 月维度：传递整个月的范围
            tarStartFormatted = formatMomentToYYYYMMDD(tarDate.clone().startOf('month'));
            tarEndFormatted = formatMomentToYYYYMMDD(tarDate.clone().endOf('month'));
            if (baseStartDate) {
              baseStartFormatted = formatMomentToYYYYMMDD(baseStartDate.clone().startOf('month'));
              baseEndFormatted = formatMomentToYYYYMMDD(baseStartDate.clone().endOf('month'));
            }
          } else if (modalDateType === '年') {
            // 年维度：传递整年的范围
            tarStartFormatted = formatMomentToYYYYMMDD(tarDate.clone().startOf('year'));
            tarEndFormatted = formatMomentToYYYYMMDD(tarDate.clone().endOf('year'));
            if (baseStartDate) {
              baseStartFormatted = formatMomentToYYYYMMDD(baseStartDate.clone().startOf('year'));
              baseEndFormatted = formatMomentToYYYYMMDD(baseStartDate.clone().endOf('year'));
            }
          }

          data.tar_start_date = tarStartFormatted;
          data.tar_end_date = tarEndFormatted;
          if (baseStartFormatted && baseEndFormatted) {
            data.base_start_date = baseStartFormatted;
            data.base_end_date = baseEndFormatted;
          }

        } else {
          // 日维度：传递具体日期，使用统一的格式化函数
          data.tar_date = formatMomentToYYYYMMDD(tarDate);
          if (baseStartDate) {
            data.base_date = formatMomentToYYYYMMDD(baseStartDate);
          }

        }
      } else {
        // 日期范围模式：传递目标日期范围和对比日期范围，使用统一的格式化函数
        data.tar_start_date = formatMomentToYYYYMMDD(tarStartDate);
        data.tar_end_date = formatMomentToYYYYMMDD(tarEndDate);
        data.base_start_date = formatMomentToYYYYMMDD(baseStartDate);
        data.base_end_date = formatMomentToYYYYMMDD(baseEndDate);

      }
      
      // 调用获取红黑榜TOP3的API接口
      const response = await API.getTopRanking(data);
      
              if (response.status === 'success') {
        // 处理返回的数据
        setSheetsData(response.sheets_data || {});
        setRankingMarketData(response.market_data || null);
        
        // 设置历史GMV变化数据
        if (response.historical_gmv_changes && Array.isArray(response.historical_gmv_changes)) {
          setHistoricalGMVChanges(response.historical_gmv_changes);
        }
        
        // 处理供给向指标数据和分析类型 - 在这里直接设置，不依赖于 run_attribution
        if (response.supply_side_attribution) {
          console.error('🎯 在 fetchTopRanking 中设置供给向指标数据:', response.supply_side_attribution);
          setSupplySideData(response.supply_side_attribution);
        }
        
        // 处理营销向指标数据
        if (response.marketing_side_attribution) {
          console.error('💰 在 fetchTopRanking 中设置营销向指标数据:', response.marketing_side_attribution);
          setMarketingSideData(response.marketing_side_attribution);
        }
        
        if (response.analysis_type) {
          console.error('📋 在 fetchTopRanking 中设置分析类型数据:', response.analysis_type);
          setAnalysisType(response.analysis_type);
        }
        
        // 保存当前的归因设置状态，用于锁定归因分析界面
        setLockedAttributionSettings({
          attributionMethods: [...attributionMethods],
          selectedAnalysisDimensions: [...selectedAnalysisDimensions],
          crossDimensionEnabled: crossDimensionEnabled,
          dimensionCombinations: JSON.parse(JSON.stringify(dimensionCombinations)),
          indicatorType: [...indicatorType]
        });
        
        // 设置默认标签页为 overall
        setActiveTab('overall');
        message.success('数据获取成功');
        setShowAttributionAnalysis(true);
        

        
        // 直接使用response中的数据调用归因分析，不依赖于state更新
        const sheetsDataToUse = response.sheets_data || {};
        const filePath = response.file_path || null; // 获取文件路径
        
        if (Object.keys(sheetsDataToUse).length > 0) {
          // 立即调用归因分析，传入文件路径
          runAttributionWithData(sheetsDataToUse, filePath);
        } else {

          message.warning('获取的数据为空，无法进行AI归因分析');
        }
        
        // 获取并保存文件路径
        setFilePath(filePath);
      } else if (response.status === 'lose') {
        message.warning(response.message || '当前筛选维度没有数据，请重新选择！');
      } else if (response.status === 'time_fail') {
        message.warning(response.message || '目标日期和对比日期不能相同，请重新选择！');
      } else {
        message.error('获取失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {

      message.error('数据获取失败: ' + (error.message || '未知错误'));
    } finally {
      setIsLoadingTopRanking(false);
    }
  };

  // 添加一个新函数，直接使用传入的数据调用归因分析，避免依赖state
  const runAttributionWithData = (_, filePath) => {
    // 开始进度条
    setIsLoading(true);
    setOutput('');
    // 注释掉清空sheets的代码，让表格在AI归因分析期间仍然可用
    // setSheets({});
    setMarketData('');

    try {
      // 获取URL中的brand参数
      const urlParams = new URLSearchParams(window.location.search);
      const brand = urlParams.get('brand') || brandFromUrl || '全部';

      // 添加调试信息


      // 调用AI归因分析API，传入文件路径、attr_index、brand、analysis_dimensions 和交叉维度参数
      API.runAttribution({
        file_path: filePath,
        attr_index: attrIndex, 
        brand: brand,
        analysis_dimensions: Array.isArray(selectedAnalysisDimensions) ? selectedAnalysisDimensions.join(',') : '',
        // 添加交叉维度参数
        cross_dimension_enabled: crossDimensionEnabled,
        dimension_combinations: JSON.stringify(dimensionCombinations),
        // 添加日期类型参数
        date_type: modalDateType,
        // 添加对比方式参数
        compare_detail_type: modalCompareDetailType,
        // 添加指标归因相关参数
        attribution_methods: Array.isArray(attributionMethods) ? attributionMethods.join(',') : '',
        indicator_types: Array.isArray(indicatorType) ? indicatorType.join(',') : ''
      }).then(response => {
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);

        handleApiResponse(response);
      }).catch(error => {
        setIsLoading(false);
        setOutput('服务器错误，请稍后再试');
        message.error('分析请求失败: ' + (error.message || '未知错误'));

      });
    } catch (error) {
      setIsLoading(false);
      setOutput('处理数据时出错');
      message.error('处理数据失败: ' + (error.message || '未知错误'));

    }
  };

  // 修改原来的runAnalysis函数，让它调用新的runAttributionWithData函数

  // 在 API 调用后处理数据
  const handleApiResponse = (response) => {
    console.error('🔥🔥🔥 handleApiResponse 被调用了！！！');
    console.error('=== handleApiResponse 调试信息 ===');
    console.error('完整响应:', response);
    console.error('response.supply_side_attribution:', response.supply_side_attribution);
    console.error('response.analysis_type:', response.analysis_type);
    
    if (response.status === 'success') {
      console.error('✅ 响应状态为 success');
      
      // 只有当response中包含sheets数据时才更新，避免AI归因分析覆盖表格数据
      if (response.sheets && Object.keys(response.sheets).length > 0) {
        console.error('📊 设置 sheets 数据');
        setSheets(response.sheets);
      }
      
      // 处理供给向指标数据
      if (response.supply_side_attribution) {
        console.error('🎯 设置供给向指标数据:', response.supply_side_attribution);
        setSupplySideData(response.supply_side_attribution);
        console.error('🎯 供给向指标数据设置完成');
      } else {
        console.error('❌ 没有供给向指标数据');
      }
      
      // 处理营销向指标数据
      if (response.marketing_side_attribution) {
        console.error('💰 设置营销向指标数据:', response.marketing_side_attribution);
        setMarketingSideData(response.marketing_side_attribution);
        console.error('💰 营销向指标数据设置完成');
      } else {
        console.error('❌ 没有营销向指标数据');
      }
      
      // 处理分析类型数据
      if (response.analysis_type) {
        console.error('📋 设置分析类型数据:', response.analysis_type);
        setAnalysisType(response.analysis_type);
        console.error('📋 分析类型数据设置完成');
      } else {
        console.error('❌ 没有分析类型数据');
      }
      
      setMarketData(response.market_data || '');
      // 修改这里，优先使用attribution_result字段，如果没有则使用result字段
      setOutput(response.attribution_result || response.result || '');
      
      console.error('🔥🔥🔥 handleApiResponse 处理完成！！！');
    } else {
      console.error('❌ 响应状态不是 success:', response.status, response.message);
      setOutput(`错误: ${response.message || '未知错误'}`);
    }
  };

  // 渲染TOP3数据组件
  const renderTop3Data = () => {
    // 如果没有数据，显示暂无数据
    if (!sheetsData || Object.keys(sheetsData).length === 0) {
      return <div style={{ padding: '20px', textAlign: 'center', marginBottom: '20px' }}>暂无TOP3数据</div>;
    }

    // 获取当前活动标签页对应的数据
    const currentDimension = activeTab || 'overall'; // 假设有 activeTab 状态
    const currentData = sheetsData[currentDimension];

    if (!Array.isArray(currentData)) {
      return <div style={{ padding: '20px', textAlign: 'center' }}>暂无数据</div>;
    }

    // 从当前维度数据中提取详细信息
    const allItems = currentData.map(item => {
      // 根据维度类型确定名称字段
      const name = 
        currentDimension === '优惠力度' ? item['优惠力度'] :
        currentDimension === '券机制' ? item['券机制'] :
        currentDimension === '券门槛' ? item['券门槛'] :
        currentDimension === '子品牌' ? item['子品牌'] :
        currentDimension === '省份' ? item['省份'] :
        currentDimension === '零售商' ? item['零售商'] :
        currentDimension === '城市' ? item['城市'] :
        currentDimension === '平台' ? item['平台'] :
        currentDimension === '商品名称' ? item['商品名称'] :
        currentDimension === '商品' ? item['商品'] :
        item['名称'] || item['商品名称'] || item['省份'] || item['零售商'];
      
      // 根据attrIndex确定使用哪个字段
      const isActivityGMV = attrIndex === '活动GMV';
      const contributionField = isActivityGMV ? '活动GMV贡献度' : 'GMV贡献度';
      const targetGMVField = isActivityGMV ? '当期活动GMV' : '目标GMV';
      const changeValueField = isActivityGMV ? '活动GMV变化值' : 'GMV变化值';
      const changeRateField = isActivityGMV ? '活动GMV变化率' : 'GMV变化率';
      const targetGMVRatioField = isActivityGMV ? '当期活动GMV占比' : '当期GMV占比';

      // 提取各项数据
      const contribution = parseFloat((item[contributionField] || '0%').replace('%', ''));
      const targetGMV = item[targetGMVField] || 0;
      const changeValue = item[changeValueField] || 0;
      const changeRate = parseFloat((item[changeRateField] || '0%').replace('%', ''));
      const targetGMVRatio = item[targetGMVRatioField] || '0.00%';

      // 确定category
      const category =
        currentDimension === '优惠力度' ? '优惠力度' :
        currentDimension === '券机制' ? '券机制' :
        currentDimension === '券门槛' ? '券门槛' :
        currentDimension === '子品牌' ? '子品牌' :
        currentDimension === '省份' ? '省份' :
        currentDimension === '零售商' ? '零售商' :
        currentDimension === '城市' ? '城市' :
        currentDimension === '平台' ? '平台' :
        currentDimension === '商品名称' ? '商品' :
        currentDimension === '商品' ? '商品' :
        currentDimension === 'product' ? '商品' :
        currentDimension === 'region' ? '省份' :
        currentDimension === 'retailer' ? '零售商' :
        currentDimension === 'sub_brand' ? '子品牌' :
        currentDimension === 'overall' ? '整体' : '其他';

      return {
        name,
        contribution,
        targetGMV,
        changeValue,
        changeRate,
        targetGMVRatio,
        category
      };
    });

    // 按贡献度排序
    const positiveItems = allItems
      .filter(item => item.contribution > 0)
      .sort((a, b) => b.contribution - a.contribution)
      .slice(0, 3);

    const negativeItems = allItems
      .filter(item => item.contribution < 0)
      .sort((a, b) => a.contribution - b.contribution)
      .slice(0, 3);

    if (positiveItems.length === 0 && negativeItems.length === 0) {
      return <div style={{ padding: '20px', textAlign: 'center' }}>暂无TOP3数据</div>;
    }

    // 格式化数字的辅助函数
    const formatNumber = (num) => {
      if (num === null || num === undefined || num === '-') return '0';
      const number = parseFloat(num);
      if (isNaN(number)) return '0';
      return Math.round(number).toLocaleString('zh-CN');
    };

    // 格式化变化值的辅助函数
    const formatChangeValue = (num) => {
      if (num === null || num === undefined || num === '-') return '0';
      const number = parseFloat(num);
      if (isNaN(number)) return '0';
      const formatted = Math.round(number).toLocaleString('zh-CN');
      return number >= 0 ? `+${formatted}` : formatted;
    };

    // 格式化变化率的辅助函数
    const formatChangeRate = (num) => {
      if (num === null || num === undefined || isNaN(num)) return '0.00%';
      return num >= 0 ? `+${num.toFixed(2)}%` : `${num.toFixed(2)}%`;
    };

    // 格式化贡献度的辅助函数
    const formatContribution = (num) => {
      if (num === null || num === undefined || isNaN(num)) return '0.00%';
      return num >= 0 ? `+${num.toFixed(2)}%` : `${num.toFixed(2)}%`;
    };

    return (
      <div className="top3-container" style={{ marginBottom: '20px', padding: '20px' }}>
        <h3 style={{ marginBottom: '20px' }}>贡献度红黑榜</h3>
        
        <div style={{ marginBottom: '30px' }}>
          <h4 style={{ color: '#52c41a', marginBottom: '15px' }}>正向贡献Top3</h4>
          {positiveItems.length > 0 ? (
            <ul style={{ listStyle: 'none', padding: 0 }}>
              {positiveItems.map((item, index) => (
                <li key={`positive-${index}`} style={{ marginBottom: '10px', lineHeight: '1.5' }}>
                  <span>
                    {`${index + 1}. ${item.name}（${item.category}）：`}
                    <span style={{ color: '#52c41a' }}>
                      {`当期GMV ${formatNumber(item.targetGMV)}，占整体比重${item.targetGMVRatio}，GMV增长${formatChangeValue(item.changeValue)}，GMV增长率${formatChangeRate(item.changeRate)}，对整体增长的贡献度${formatContribution(item.contribution)}`}
                    </span>
                  </span>
                </li>
              ))}
            </ul>
          ) : (
            <div>暂无数据</div>
          )}
        </div>

        <div>
          <h4 style={{ color: '#f5222d', marginBottom: '15px' }}>负向贡献Top3</h4>
          {negativeItems.length > 0 ? (
            <ul style={{ listStyle: 'none', padding: 0 }}>
              {negativeItems.map((item, index) => (
                <li key={`negative-${index}`} style={{ marginBottom: '10px', lineHeight: '1.5' }}>
                  <span>
                    {`${index + 1}. ${item.name}（${item.category}）：`}
                    <span style={{ color: '#f5222d' }}>
                      {`当期GMV ${formatNumber(item.targetGMV)}，占整体比重${item.targetGMVRatio}，GMV增长${formatChangeValue(item.changeValue)}，GMV增长率${formatChangeRate(item.changeRate)}，对整体增长的贡献度${formatContribution(item.contribution)}`}
                    </span>
                  </span>
                </li>
              ))}
            </ul>
          ) : (
            <div>暂无数据</div>
          )}
        </div>
      </div>
    );
  };

  // 添加标签页切换处理函数

  // 判断GMV变化是否异动
  const analyzeGMVAnomaly = (historicalData, currentGMVChange, currentGMVChangeRate) => {
    if (!historicalData || historicalData.length === 0 || !currentGMVChange) {
      return { isAnomaly: false, message: '数据不足，无法判断是否异动' };
    }

    try {
      // 计算历史GMV变化率的均值和标准差，使用原始值（不取绝对值）
      const gmvChangeRates = historicalData.map(item => item.gmv_change_rate);
      const mean = gmvChangeRates.reduce((sum, rate) => sum + rate, 0) / gmvChangeRates.length;
      const variance = gmvChangeRates.reduce((sum, rate) => sum + Math.pow(rate - mean, 2), 0) / gmvChangeRates.length;
      const stdDev = Math.sqrt(variance);

      // 设定异动阈值为1个标准差
      const threshold = 1;
      const zScore = Math.abs(currentGMVChangeRate - mean) / stdDev;
      
      // 判断是否异动
      const isAnomaly = zScore > threshold;
      
      // 构建异动说明
      let message = isAnomaly 
        ? `当前GMV变化率${currentGMVChangeRate.toFixed(2)}%，相比历史平均GMV变化率${mean.toFixed(2)}%，出现异常波动。` 
        : `当前GMV变化率${currentGMVChangeRate.toFixed(2)}%，相比历史平均GMV变化率${mean.toFixed(2)}%，未出现明显异常波动`;

      return { isAnomaly, message, zScore, mean, stdDev };
    } catch (error) {

      return { isAnomaly: false, message: '分析过程中发生错误，无法判断是否异动' };
    }
  };

  // 生成历史GMV变化率的echarts配置（单日期模式）
  const getGMVChartOption = (historicalData, anomalyInfo) => {
    if (!historicalData || historicalData.length === 0) {
      return {};
    }

    // 从historicalData中提取日期和变化率
    // 根据modalDateType决定日期格式化方式
    const dates = historicalData.map(item => {
      if (modalDateType === '月' && item.date.match(/^\d{4}-\d{2}$/)) {
        // 月级别数据，直接使用原始格式 "2024-07"
        return item.date;
      } else {
        // 日级别数据，格式化为 "YYYY-MM-DD"
        return moment(item.date).format('YYYY-MM-DD');
      }
    });
    const gmvChangeRates = historicalData.map(item => item.gmv_change_rate);
    
    // 计算均值线
    const meanLineData = new Array(dates.length).fill(anomalyInfo?.mean || 0);
    
    // 计算标准差边界线 - 均值加减1倍标准差（改为1倍标准差）
    const upperBoundLineData = new Array(dates.length).fill((anomalyInfo?.mean || 0) + 1 * (anomalyInfo?.stdDev || 0));
    const lowerBoundLineData = new Array(dates.length).fill((anomalyInfo?.mean || 0) - 1 * (anomalyInfo?.stdDev || 0));

    // 获取目标日期的索引（如果当前日期存在于历史数据中）
    let targetDateStr = null;
    if (tarDate) {
      if (modalDateType === '月') {
        // 月级别数据，使用 "YYYY-MM" 格式
        targetDateStr = tarDate.format('YYYY-MM');
      } else {
        // 日级别数据，使用 "YYYY-MM-DD" 格式
        targetDateStr = tarDate.format('YYYY-MM-DD');
      }
    }
    const targetDateIndex = targetDateStr ? dates.indexOf(targetDateStr) : -1;

    return {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          // 确保直接使用原始数据中的值
          const index = params[0].dataIndex;
          const originalRate = historicalData[index]?.gmv_change_rate;
          return `${params[0].axisValue}<br/>变化率: ${originalRate.toFixed(2)}%`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      legend: {
        data: [
          'GMV变化率',
          '均值线',
          '上限(+1σ)',
          '下限(-1σ)',
          '零轴线',
          {
            name: '目标日期',
            itemStyle: {
              color: 'rgba(245, 34, 45, 0.5)' // 设置图例项颜色为浅红色
            }
          }
        ],
        bottom: '5%'
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          rotate: 45,
          fontSize: 10
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: 'GMV变化率',
          type: 'line',
          data: gmvChangeRates,
          smooth: true,
          lineStyle: {
            width: 2,
          },
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(24,144,255,0.3)'
              }, {
                offset: 1, color: 'rgba(24,144,255,0.1)'
              }]
            }
          }
        },
        {
          name: '均值线',
          type: 'line',
          data: meanLineData,
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: '#52c41a'
          },
          tooltip: {
            show: false
          }
        },
        {
          name: '上限(+1σ)',
          type: 'line',
          data: upperBoundLineData,
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: '#faad14'
          },
          tooltip: {
            show: false
          }
        },
        {
          name: '下限(-1σ)',
          type: 'line',
          data: lowerBoundLineData,
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: '#faad14'
          },
          tooltip: {
            show: false
          }
        },
        {
          name: '零轴线',
          type: 'line',
          data: new Array(dates.length).fill(0),
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'solid',
            color: '#999999'
          },
          tooltip: {
            show: false
          }
        },
        {
          name: '目标日期',
          type: 'line',
          markLine: {
            silent: true, // 使标线不响应鼠标事件
            symbol: ['none', 'none'], // 不显示端点符号
            data: targetDateIndex !== -1 ? [
              {
                xAxis: targetDateIndex,
                lineStyle: {
                  color: 'rgba(245, 34, 45, 0.5)', // 浅红色
                  width: 1, // 线宽设置为1
                  type: 'solid'
                },
                label: { show: false } // 确保不显示标签
              }
            ] : [],
          },
          // 确保这个系列不显示实际数据线
          data: new Array(dates.length).fill(null),
        }
      ]
    };
  };

  // 生成历史GMV变化率的echarts配置（日期范围模式）
  const getGMVChartOptionForRange = (historicalData, anomalyInfo) => {
    if (!historicalData || historicalData.length === 0) {
      return {};
    }

    // 从historicalData中提取日期和变化率
    // 根据modalDateType决定日期格式化方式
    const dates = historicalData.map(item => {
      if (modalDateType === '月' && item.date.match(/^\d{4}-\d{2}$/)) {
        // 月级别数据，直接使用原始格式 "2024-07"
        return item.date;
      } else {
        // 日级别数据，格式化为 "YYYY-MM-DD"
        return moment(item.date).format('YYYY-MM-DD');
      }
    });
    const gmvChangeRates = historicalData.map(item => item.gmv_change_rate);
    
    // 计算均值线
    const meanLineData = new Array(dates.length).fill(anomalyInfo?.mean || 0);
    
    // 计算标准差边界线 - 均值加减1倍标准差
    const upperBoundLineData = new Array(dates.length).fill((anomalyInfo?.mean || 0) + 1 * (anomalyInfo?.stdDev || 0));
    const lowerBoundLineData = new Array(dates.length).fill((anomalyInfo?.mean || 0) - 1 * (anomalyInfo?.stdDev || 0));

    // 获取目标日期范围的索引
    let targetStartDateStr, targetEndDateStr;

    // 处理不同的日期模式
    if (tarStartDate && tarEndDate) {
      // 明确的日期范围模式
      if (modalDateType === '月') {
        // 月级别数据，使用 "YYYY-MM" 格式
        targetStartDateStr = tarStartDate.format('YYYY-MM');
        targetEndDateStr = tarEndDate.format('YYYY-MM');
      } else {
        // 日级别数据，使用 "YYYY-MM-DD" 格式
        targetStartDateStr = tarStartDate.format('YYYY-MM-DD');
        targetEndDateStr = tarEndDate.format('YYYY-MM-DD');
      }
    } else if (tarDate && (modalDateType === '月' || modalDateType === '年')) {
      // 指定日期模式但日期类型为月或年
      if (modalDateType === '月') {
        // 月级别数据，直接使用月份格式
        targetStartDateStr = tarDate.format('YYYY-MM');
        targetEndDateStr = tarDate.format('YYYY-MM');
      } else if (modalDateType === '年') {
        targetStartDateStr = tarDate.clone().startOf('year').format('YYYY-MM-DD');
        targetEndDateStr = tarDate.clone().endOf('year').format('YYYY-MM-DD');
      }
    } else {
      targetStartDateStr = null;
      targetEndDateStr = null;
    }
    
    const targetStartIndex = targetStartDateStr ? dates.indexOf(targetStartDateStr) : -1;
    const targetEndIndex = targetEndDateStr ? dates.indexOf(targetEndDateStr) : -1;



    // 构建标记线数据 - 为开始和结束日期分别创建标记线
    const markLineData = [];
    
    // 添加目标开始日期标记线
    if (targetStartIndex !== -1) {
      markLineData.push({
        xAxis: targetStartIndex,
        lineStyle: {
          color: 'rgba(245, 34, 45, 0.5)', // 与单日期模式保持一致的浅红色
          width: 1, // 与单日期模式保持一致的线宽
          type: 'solid'
        },
        label: { show: false } // 不显示标签
      });
    }
    
    // 添加目标结束日期标记线
    if (targetEndIndex !== -1 && targetEndIndex !== targetStartIndex) {
      markLineData.push({
        xAxis: targetEndIndex,
        lineStyle: {
          color: 'rgba(245, 34, 45, 0.5)', // 与单日期模式保持一致的浅红色
          width: 1, // 与单日期模式保持一致的线宽
          type: 'solid'
        },
        label: { show: false } // 不显示标签
      });
    }

    return {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          // 确保直接使用原始数据中的值
          const index = params[0].dataIndex;
          const originalRate = historicalData[index]?.gmv_change_rate;
          return `${params[0].axisValue}<br/>变化率: ${originalRate.toFixed(2)}%`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      legend: {
        data: [
          'GMV变化率',
          '均值线',
          '上限(+1σ)',
          '下限(-1σ)',
          '零轴线',
          {
            name: '目标日期范围',
            itemStyle: {
              color: 'rgba(245, 34, 45, 0.5)' // 与单日期模式保持一致的浅红色
            }
          }
        ],
        bottom: '5%'
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          rotate: 45,
          fontSize: 10
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: 'GMV变化率',
          type: 'line',
          data: gmvChangeRates,
          smooth: true,
          lineStyle: {
            width: 2,
          },
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(24,144,255,0.3)'
              }, {
                offset: 1, color: 'rgba(24,144,255,0.1)'
              }]
            }
          }
        },
        {
          name: '均值线',
          type: 'line',
          data: meanLineData,
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: '#52c41a'
          },
          tooltip: {
            show: false
          }
        },
        {
          name: '上限(+1σ)',
          type: 'line',
          data: upperBoundLineData,
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: '#faad14'
          },
          tooltip: {
            show: false
          }
        },
        {
          name: '下限(-1σ)',
          type: 'line',
          data: lowerBoundLineData,
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: '#faad14'
          },
          tooltip: {
            show: false
          }
        },
        {
          name: '零轴线',
          type: 'line',
          data: new Array(dates.length).fill(0),
          symbol: 'none',
          lineStyle: {
            width: 1,
            type: 'solid',
            color: '#999999'
          },
          tooltip: {
            show: false
          }
        },
        {
          name: '目标日期范围',
          type: 'line',
          markLine: {
            silent: true, // 使标线不响应鼠标事件
            symbol: ['none', 'none'], // 不显示端点符号
            data: markLineData,
          },
          // 确保这个系列不显示实际数据线
          data: new Array(dates.length).fill(null),
        }
      ]
    };
  };

  // 添加城市下拉框点击处理函数 - 需要先选择完整日期
  const handleCityDropdownClick = () => {
    if (!validateDatesComplete()) {
      message.warning('请先选择完整的对比日期！');
      return;
    }
    setCityDropdownVisible(!cityDropdownVisible);
    fetchCities();
  };

  // 添加城市筛选框输入变化处理函数

  // 添加城市选择处理函数 - 改为支持多选，自动处理"全部"选项
  const handleCityChange = (values) => {
    let newValues = [...values];
    
    // 如果选择了"全部"以外的选项，移除"全部"
    if (newValues.includes('全部') && newValues.length > 1) {
      newValues = newValues.filter(item => item !== '全部');
    }
    
    // 如果没有选择任何项，自动添加"全部"
    if (newValues.length === 0) {
      newValues = ['全部'];
    }
    
    setCity(newValues);
    setCityDropdownVisible(false);
  };

  // 添加城市选择处理函数（与下拉框交互）

  // 时间对比弹框处理函数
  const handleTimeCompareSettingClick = () => {
    // 确保打开弹框时默认选择"同环比"
    setModalCompareMethod('同环比');
    setTimeCompareModalVisible(true);
  };

  const handleTimeCompareModalOk = () => {
    // 根据modalTargetDateType设置dateType
    if (modalTargetDateType === '指定日期') {
      setDateType('single');
    } else if (modalTargetDateType === '日期范围') {
      setDateType('range');
    }
    
    setTimeCompareModalVisible(false);
    // 确保计算对比日期
    if (modalCompareMethod === '同环比') {
      // 对比日期计算逻辑
      
      // 确保对比日期计算正确
      setTimeout(() => {
        updateCompareDates();
      }, 0);
    }
  };

  const handleTimeCompareModalCancel = () => {
    setTimeCompareModalVisible(false);
  };

  // 归因设置弹框处理函数
  const handleAttributionSettingClick = () => {
    setAttributionModalVisible(true);
  };

  const handleAttributionModalOk = () => {
    // 验证是否选择了归因方式
    if (!attributionMethods || attributionMethods.length === 0) {
      message.warning('请至少选择一种归因方式！');
      return;
    }
    
    // 如果选择了维度归因，验证是否选择了分析维度
    if (attributionMethods.includes('维度') && (!selectedAnalysisDimensions || selectedAnalysisDimensions.length === 0)) {
      message.warning('请选择分析维度！');
      return;
    }
    
    // 如果选择了指标归因，验证是否选择了指标类型
    if (attributionMethods.includes('指标') && (!indicatorType || indicatorType.length === 0)) {
      message.warning('请选择指标类型！');
      return;
    }
    
    // 检查是否修改了归因设置（如果已经执行过一键归因）
    if (showAttributionAnalysis) {
      const hasChanged = 
        JSON.stringify(attributionMethods) !== JSON.stringify(lockedAttributionSettings.attributionMethods) ||
        JSON.stringify(selectedAnalysisDimensions) !== JSON.stringify(lockedAttributionSettings.selectedAnalysisDimensions) ||
        crossDimensionEnabled !== lockedAttributionSettings.crossDimensionEnabled ||
        JSON.stringify(dimensionCombinations) !== JSON.stringify(lockedAttributionSettings.dimensionCombinations) ||
        JSON.stringify(indicatorType) !== JSON.stringify(lockedAttributionSettings.indicatorType);
      
      if (hasChanged) {
        message.info('归因设置已修改，请重新执行"一键归因"以应用新设置。');
      }
    }
    
    setAttributionModalVisible(false);
  };

  const handleAttributionModalCancel = () => {
    setAttributionModalVisible(false);
  };

  // 归因方式变化处理
  const handleAttributionMethodsChange = (values) => {
    setAttributionMethods(values);
    
    // 如果当前活动标签页不在选中的方式中，切换到第一个选中的方式
    if (values.length > 0 && !values.includes(activeAttributionTab)) {
      setActiveAttributionTab(values[0]);
      
      // 如果选择了维度归因，初始化相关状态
      if (values[0] === '维度') {
        setCurrentAttributionTab('维度归因');
        setCurrentDimensionType('单一维度');
        if (selectedAnalysisDimensions.length > 0) {
          setCurrentDimensionTab(selectedAnalysisDimensions[0]);
        }
      } else if (values[0] === '指标') {
        setCurrentAttributionTab('指标归因');
      }
    }
  };

  // 分析维度变化处理
  const handleAnalysisDimensionsChange = (values) => {
    setSelectedAnalysisDimensions(values);
  };

  // 获取可用的分析维度选项（根据归因指标类型）
  const getAnalysisDimensionOptions = () => {
    if (attrIndex === '全量GMV') {
      return [
        { label: '平台', value: '平台' },
        { label: '省份', value: '省份' },
        { label: '城市', value: '城市' },
        { label: '零售商', value: '零售商' },
        { label: '子品牌', value: '子品牌' },
        { label: '商品名称', value: '商品名称' }
      ];
    } else if (attrIndex === '活动GMV') {
      return [
        { label: '平台', value: '平台' },
        { label: '省份', value: '省份' },
        { label: '城市', value: '城市' },
        { label: '零售商', value: '零售商' },
        { label: '子品牌', value: '子品牌' },
        { label: '商品名称', value: '商品名称' },
        { label: '券机制', value: '券机制' },
        { label: '券门槛', value: '券门槛' },
        { label: '优惠力度', value: '优惠力度' }
      ];
    }
    return [];
  };

  // 交叉维度归因处理函数
  const handleCrossDimensionChange = (checked) => {
    setCrossDimensionEnabled(checked);
    if (!checked) {
      // 如果取消勾选，重置组合
      setDimensionCombinations([{ id: 1, dimensions: [] }]);
    }
  };

  // 添加新的维度组合
  const addDimensionCombination = () => {
    const newId = Math.max(...dimensionCombinations.map(c => c.id)) + 1;
    setDimensionCombinations([...dimensionCombinations, { id: newId, dimensions: [] }]);
  };

  // 删除维度组合
  const removeDimensionCombination = (id) => {
    if (dimensionCombinations.length > 1) {
      setDimensionCombinations(dimensionCombinations.filter(c => c.id !== id));
    }
  };

  // 更新组合中的维度选择
  const updateCombinationDimensions = (id, dimensions) => {
    setDimensionCombinations(dimensionCombinations.map(c => 
      c.id === id ? { ...c, dimensions } : c
    ));
  };

  // 获取指标关系公式
  const getIndicatorFormulas = (type) => {
    if (type === '供给向') {
      return [
        'GMV = 铺货门店数 × 店均在售SKU数 × SKU平均动销率 × 动销SKU平均GMV'
      ];
    }
    if (type === '营销向') {
      return [
        'GMV = 活动GMV + 自然GMV',
        'GMV = 活动机制GMV + 自然GMV',
        'GMV = 活动机制核销金额 × 活动机制活动ROI + 自然GMV'
      ];
    }
    return [];
  };

  // 根据日期类型设置禁用日期的规则
  const getDisabledDateByType = (dateType) => {
    return (current) => {
      if (!current) return false;
      
      const now = moment();
      
      switch (dateType) {
        case '日':
  // 禁用今天和未来日期
          return current >= now.startOf('day');
        case '月':
          // 禁用本月和未来月份
          return current >= now.startOf('month');
        case '年':
          // 禁用今年和未来年份
          return current >= now.startOf('year');
        default:
          return current >= now.startOf('day');
      }
    };
  };

  // 根据日期类型获取DatePicker的picker属性
  const getPickerType = (dateType) => {
    switch (dateType) {
      case '日':
        return 'date';
      case '月':
        return 'month';
      case '年':
        return 'year';
      default:
        return 'date';
    }
  };

  // 根据日期类型获取格式
  const getDateFormat = (dateType) => {
    switch (dateType) {
      case '日':
        return 'YYYY-MM-DD';
      case '月':
        return 'YYYY-MM';
      case '年':
        return 'YYYY';
      default:
        return 'YYYY-MM-DD';
    }
  };

  // 新增：根据日期维度类型格式化日期显示
  const formatDateDisplay = (momentDate, dateType) => {
    if (!momentDate) return '未选择';
    
    switch(dateType) {
      case '日':
        return momentDate.format('YYYY-MM-DD');
      case '月':
        return momentDate.format('YYYY-MM');
      case '年':
        return momentDate.format('YYYY');
      default:
        return momentDate.format('YYYY-MM-DD');
    }
  };

  // 根据日期类型获取占位符文本
  const getPlaceholderByType = (dateType, isRange = false) => {
    if (isRange) {
      switch (dateType) {
        case '日':
          return ['开始日期', '结束日期'];
        case '月':
          return ['开始月份', '结束月份'];
        case '年':
          return ['开始年份', '结束年份'];
        default:
          return ['开始日期', '结束日期'];
      }
    } else {
      switch (dateType) {
        case '日':
          return '选择日期';
        case '月':
          return '选择月份';
        case '年':
          return '选择年份';
        default:
          return '选择日期';
      }
    }
  };

  // 根据日期类型获取可用的对比方式选项
  const getCompareDetailOptions = (dateType) => {
    switch (dateType) {
      case '日':
        return [
          { value: '日环比', label: '日环比' },
          { value: '周同比', label: '周同比' },
          { value: '月同比', label: '月同比' },
          { value: '年同比', label: '年同比' },
          { value: '年同比(按周)', label: '年同比(按周)' },
        ];
      case '月':
        return [
          { value: '月环比', label: '月环比' },
          { value: '年同比', label: '年同比' },
        ];
      case '年':
        return [
          { value: '年同比', label: '年同比' },
        ];
      default:
        return [
          { value: '日环比', label: '日环比' },
        ];
    }
  };

  // 当日期类型改变时更新对比方式
  useEffect(() => {
    // 获取当前日期类型的对比选项
    const options = getCompareDetailOptions(modalDateType);
    
    // 如果当前选择的对比方式不在新的选项列表中，设置为第一个选项
    if (!options.some(option => option.value === modalCompareDetailType)) {
      setModalCompareDetailType(options[0].value);
    }
  }, [modalDateType, modalCompareDetailType]);

  // 计算对比日期函数 - 使用useCallback避免依赖问题
  const updateCompareDates = useCallback(() => {
    // 仅处理同环比模式
    if (modalCompareMethod !== '同环比') return;
    
    // 单日期模式
    if (modalTargetDateType === '指定日期' && tarDate) {
      // 处理不同的对比类型
      let compareDate;
      const targetDate = tarDate; // 直接使用tarDate，确保使用用户选择的日期
      
      switch(modalCompareDetailType) {
        case '日环比':
          compareDate = targetDate.clone().subtract(1, 'days');
          break;
        case '周同比':
          compareDate = targetDate.clone().subtract(1, 'weeks');
          break;
        case '月同比':
          compareDate = targetDate.clone().subtract(1, 'months');
          break;
        case '月环比':
          compareDate = targetDate.clone().subtract(1, 'months');
          break;
        case '年同比':
          compareDate = targetDate.clone().subtract(1, 'years');
          break;
        case '年同比(按周)':
          // 获取目标日期是一年中的第几周和星期几
          const weekOfYear = targetDate.week();
          const dayOfWeek = targetDate.day();
          compareDate = targetDate.clone().subtract(1, 'years').week(weekOfYear).day(dayOfWeek);
          break;
        default:
          compareDate = targetDate.clone();
      }
      

      setBaseStartDate(compareDate);
    } 
    // 日期范围模式
    else if (modalTargetDateType === '日期范围' && tarStartDate && tarEndDate) {
      const startDate = tarStartDate; // 直接使用tarStartDate
      const endDate = tarEndDate; // 直接使用tarEndDate
      let compareStartDate, compareEndDate;
      
      // 计算目标日期范围的跨度
      const targetDuration = moment.duration(endDate.diff(startDate));
      const days = targetDuration.asDays();
      
      switch(modalCompareDetailType) {
        case '日环比':
          compareStartDate = startDate.clone().subtract(1, 'days');
          compareEndDate = endDate.clone().subtract(1, 'days');
          break;
        case '周同比':
          compareStartDate = startDate.clone().subtract(1, 'weeks');
          compareEndDate = endDate.clone().subtract(1, 'weeks');
          break;
        case '月同比':
          compareStartDate = startDate.clone().subtract(1, 'months');
          compareEndDate = endDate.clone().subtract(1, 'months');
          break;
        case '月环比':
          compareStartDate = startDate.clone().subtract(1, 'months');
          compareEndDate = endDate.clone().subtract(1, 'months');
          break;
        case '年同比':
          compareStartDate = startDate.clone().subtract(1, 'years');
          compareEndDate = endDate.clone().subtract(1, 'years');
          break;
        case '年同比(按周)':
          // 获取开始日期是一年中的第几周和星期几
          const startWeekOfYear = startDate.week();
          const startDayOfWeek = startDate.day();
          compareStartDate = startDate.clone().subtract(1, 'years').week(startWeekOfYear).day(startDayOfWeek);
          
          // 获取结束日期是一年中的第几周和星期几
          const endWeekOfYear = endDate.week();
          const endDayOfWeek = endDate.day();
          compareEndDate = endDate.clone().subtract(1, 'years').week(endWeekOfYear).day(endDayOfWeek);
          break;
        default:
          // 默认保持相同跨度
          compareStartDate = startDate.clone().subtract(days + 1, 'days');
          compareEndDate = startDate.clone().subtract(1, 'days');
      }
      

      setBaseStartDate(compareStartDate);
      setBaseEndDate(compareEndDate);
    }
  }, [modalCompareMethod, modalTargetDateType, modalCompareDetailType, tarDate, tarStartDate, tarEndDate]);

  // 修改监听逻辑，确保在所有相关状态变化时重新计算对比日期
  useEffect(() => {
    if (modalCompareMethod === '同环比') {
      updateCompareDates();
    }
  }, [
    modalCompareMethod,
    modalCompareDetailType,
    modalTargetDateType,
    modalDateType,
    tarDate,
    tarStartDate,
    tarEndDate,
    updateCompareDates
  ]);

  // 使用直接设置函数处理目标日期变化 - 不自动计算对比日期
  const handleTarDateChangeWithCompare = (date) => {
    console.log('用户选择目标日期:', date ? date.format('YYYY-MM-DD') : null);
    setTarDate(date);
    // 注释掉自动对比日期计算，让用户手动选择对比日期
    // 这样确保用户看到的日期就是实际传递给后端的日期
  };

  // 处理目标日期范围开始日期变化 - 不自动计算对比日期
  const handleTarStartDateChangeWithCompare = (date) => {
    console.log('用户选择目标开始日期:', date ? date.format('YYYY-MM-DD') : null);
    setTarStartDate(date);
    // 注释掉自动对比日期计算，让用户手动选择对比日期
    // 这样确保用户看到的日期就是实际传递给后端的日期
  };

  // 处理目标日期范围结束日期变化 - 不自动计算对比日期
  const handleTarEndDateChangeWithCompare = (date) => {
    console.log('用户选择目标结束日期:', date ? date.format('YYYY-MM-DD') : null);
    setTarEndDate(date);
    // 注释掉自动对比日期计算，让用户手动选择对比日期
    // 这样确保用户看到的日期就是实际传递给后端的日期
  };

  // 在modalCompareDetailType变化时也重新计算对比日期
  useEffect(() => {
    if (modalCompareMethod === '同环比') {
      // 确保所有必要的值都存在
      if ((modalTargetDateType === '指定日期' && tarDate) || 
          (modalTargetDateType === '日期范围' && tarStartDate && tarEndDate)) {
        updateCompareDates();
      }
    }
  }, [modalCompareDetailType, modalCompareMethod, modalTargetDateType, tarDate, tarEndDate, tarStartDate, updateCompareDates]);

  // 监听时间对比变化，如果不是基于月维度的对比，则移除供给向选择
  useEffect(() => {
    if (indicatorType.includes('供给向') && modalCompareDetailType &&
        !(modalDateType === '月' || modalCompareDetailType.includes('月'))) {
      const filteredTypes = indicatorType.filter(type => type !== '供给向');
      setIndicatorType(filteredTypes);

      // 如果当前选中的指标标签页被移除，重置为第一个可用的
      if (currentIndicatorTab === '供给向' && filteredTypes.length > 0) {
        setCurrentIndicatorTab(filteredTypes[0]);
      }
    }
  }, [modalCompareDetailType, modalDateType, indicatorType, currentIndicatorTab]);

  // 在modalCompareMethod变化时重新计算对比日期
  useEffect(() => {
    if (modalCompareMethod === '同环比') {
      // 确保所有必要的值都存在
      if ((modalTargetDateType === '指定日期' && tarDate) || 
          (modalTargetDateType === '日期范围' && tarStartDate && tarEndDate)) {
        updateCompareDates();
      }
    }
  }, [modalCompareMethod, modalTargetDateType, tarDate, tarEndDate, tarStartDate, updateCompareDates]);

  // 在目标日期类型变化时重新计算对比日期
  useEffect(() => {
    if (modalCompareMethod === '同环比') {
      // 当目标日期类型变化时，更新对比日期类型匹配目标日期类型
      setModalStartDateType(modalTargetDateType);
      
      // 根据目标日期类型重新计算对比日期
      if (modalTargetDateType === '指定日期' && tarDate) {
        updateCompareDates();
      } else if (modalTargetDateType === '日期范围' && tarStartDate && tarEndDate) {
        updateCompareDates();
      }
    }
  }, [modalTargetDateType, modalCompareMethod, tarDate, tarEndDate, tarStartDate, updateCompareDates]);

  // 监听归因指标变化，更新筛选条件选中项
  useEffect(() => {
    if (attrIndex === '全量GMV') {
      setSelectedFilterConditions([]);
    } else {
      setSelectedFilterConditions([]);
    }
  }, [attrIndex]);



  // 获取可用的归因分析主标签页
  const getAvailableAttributionTabs = () => {
    // 使用锁定的归因设置而不是实时设置
    const methodsToUse = showAttributionAnalysis ? lockedAttributionSettings.attributionMethods : attributionMethods;
    return methodsToUse.filter(method => method !== '').map(method => {
      switch(method) {
        case '维度':
          return '维度归因';
        case '指标':
          return '指标归因';
        default:
          return method;
      }
    });
  };

  // 获取当前选中的单一维度标签页
  const getAvailableDimensionTabs = () => {
    // 使用锁定的归因设置而不是实时设置
    return showAttributionAnalysis ? lockedAttributionSettings.selectedAnalysisDimensions : selectedAnalysisDimensions;
  };

  // 获取当前选中的交叉维度标签页
  const getAvailableCrossTabs = () => {
    // 如果有sheetsData且包含交叉维度数据，使用实际的交叉维度名称
    if (sheetsData && Object.keys(sheetsData).length > 0) {
      const crossDimensionKeys = Object.keys(sheetsData).filter(key => 
        key.includes('-') && !['平台', '省份', '城市', '零售商', '子品牌', '商品', '券机制', '券门槛', '优惠力度'].includes(key)
      );
      if (crossDimensionKeys.length > 0) {
        return crossDimensionKeys;
      }
    }
    
    // 否则根据dimensionCombinations生成具体的组合名称
    // 使用锁定的归因设置而不是实时设置
    const combinationsToUse = showAttributionAnalysis ? lockedAttributionSettings.dimensionCombinations : dimensionCombinations;
    return combinationsToUse
      .filter(combo => combo.dimensions && combo.dimensions.length > 1)
      .map(combo => combo.dimensions.join('-'));
  };

  // 处理归因分析主标签页切换
  const handleAttributionTabChange = (tab) => {
    setCurrentAttributionTab(tab);
    
    // 切换归因分析主标签页时，清理下钻状态
    setDrillDownPath([]);
    setDrillDownData({});
    setExpandedRows(new Set());
    
    // 根据标签页类型设置默认的子标签页
    if (tab === '维度归因') {
      setCurrentDimensionType('单一维度');
      if (selectedAnalysisDimensions.length > 0) {
        setCurrentDimensionTab(selectedAnalysisDimensions[0]);
      }
    } else if (tab === '指标归因') {
      // 切换到指标归因时，初始化指标标签页
      const indicatorTypesToUse = showAttributionAnalysis ? lockedAttributionSettings.indicatorType : indicatorType;
      if (indicatorTypesToUse.length > 0) {
        setCurrentIndicatorTab(indicatorTypesToUse[0]);
      }
    }
  };

  // 处理维度类型切换（单一维度/交叉维度）
  const handleDimensionTypeChange = (type) => {
    setCurrentDimensionType(type);
    
    // 切换维度类型时，清理下钻状态
    setDrillDownPath([]);
    setDrillDownData({});
    setExpandedRows(new Set());
    
    if (type === '单一维度' && selectedAnalysisDimensions.length > 0) {
      setCurrentDimensionTab(selectedAnalysisDimensions[0]);
    } else if (type === '交叉维度') {
      const availableCrossTabs = getAvailableCrossTabs();
      if (availableCrossTabs.length > 0) {
        setCurrentCrossTab(availableCrossTabs[0]);
      }
    }
  };

  // 处理维度标签页切换
  const handleDimensionTabChange = (tab) => {
    setCurrentDimensionTab(tab);
    
    // 切换维度标签页时，清理下钻状态
    setDrillDownPath([]);
    setDrillDownData({});
    setExpandedRows(new Set());
  };

  // 处理交叉维度标签页切换
  const handleCrossTabChange = (tab) => {
    setCurrentCrossTab(tab);
    
    // 切换交叉维度标签页时，清理下钻状态
    setDrillDownPath([]);
    setDrillDownData({});
    setExpandedRows(new Set());
  };

  // 处理指标类型标签页切换
  const handleIndicatorTabChange = (tab) => {
    setCurrentIndicatorTab(tab);
  };

  // 下钻功能回调函数
  const handleDrillDown = useCallback(async (clickedValue, currentDimension, targetDimension, rowData = null) => {
    // 显示下钻loading提示
    const loadingMessage = message.loading(`正在下钻到${targetDimension}维度...`, 0);
    
    try {
      console.log('开始下钻:', { clickedValue, currentDimension, targetDimension });
      // 多层下钻处理
      
      // 判断是否是在已下钻的数据上继续下钻
      const isNestedDrillDown = drillDownPath.length > 0;
      
      // 提取UPC信息（如果当前是商品维度）
      let upc = null;
      if ((currentDimension === '商品名称' || currentDimension === '商品') && rowData && rowData.UPC) {
        upc = rowData.UPC;
      }
      
      // 创建新的路径项
      let newPath;
      let crossDimensionDataForPath = null;
      
      // 如果是第一层交叉维度下钻，需要准备交叉维度数据
      if (!isNestedDrillDown && currentDimension && currentDimension.includes('-') && rowData) {
        // 这里先临时提取交叉维度数据，稍后会在isFromCrossDimension分支中正式处理
        const crossDimensions = currentDimension.split('-');
        const tempCrossDimensionData = {};
        
        if (rowData['交叉维度名称']) {
          const crossName = rowData['交叉维度名称'];
          const values = crossName.split('-');
          
          crossDimensions.forEach((dimension, index) => {
            if (index < values.length) {
              tempCrossDimensionData[dimension] = values[index];
            }
          });
          crossDimensionDataForPath = tempCrossDimensionData;
        }
      }
      
      if (isNestedDrillDown) {
        // 如果是嵌套下钻，需要检查当前点击的值是否属于已下钻的数据
        // 在已有路径基础上添加新的下钻级别
        const newPathItem = drillDownManager.createPathItem(currentDimension, clickedValue, drillDownPath.length, upc);
        newPath = [...drillDownPath, newPathItem];
      } else {
        // 第一级下钻
        const newPathItem = drillDownManager.createPathItem(currentDimension, clickedValue, 0, upc, crossDimensionDataForPath);
        newPath = [newPathItem];
      }
      
      // 生成缓存键（支持多级嵌套）
      const drillDownKey = drillDownManager.generateNestedCacheKey(newPath, targetDimension);
      const expandedKey = isNestedDrillDown 
        ? `${drillDownPath.map(p => `${p.dimension}:${p.value}`).join('|')}_${currentDimension}_${clickedValue}`
        : `${currentDimension}_${clickedValue}`;
      
      // 检查是否已经有下钻数据
      if (drillDownData[drillDownKey]) {
        // 隐藏loading提示
        loadingMessage();
        
        // 已有数据，切换展开/折叠状态
        const newExpandedRows = new Set(expandedRows);
        if (newExpandedRows.has(expandedKey)) {
          newExpandedRows.delete(expandedKey);
          message.success(`已折叠${targetDimension}数据`);
        } else {
          newExpandedRows.add(expandedKey);
          message.success(`已展开${targetDimension}数据`);
        }
        setExpandedRows(newExpandedRows);
        return;
      }

      // 构建筛选条件（包含完整的下钻路径）
      let filters;
      
      // 检查是否在交叉维度中下钻或者是从交叉维度开始的多级下钻
      const isFromCrossDimension = (currentDimension && currentDimension.includes('-') && rowData) ||
                                   (drillDownPath.length > 0 && drillDownPath[0].dimension && drillDownPath[0].dimension.includes('-'));

      if (isFromCrossDimension) {
        // 从交叉维度数据中提取筛选条件
        let crossDimensionData = {};
        
        // 如果是第一层交叉维度下钻
        if (currentDimension && currentDimension.includes('-') && rowData) {
          const crossDimensions = currentDimension.split('-');
          
          // 检查是否有交叉维度名称字段，从中解析各维度值
          if (rowData['交叉维度名称']) {
            const crossName = rowData['交叉维度名称'];
            const values = crossName.split('-');
            
            console.log(`从交叉维度名称解析: ${crossName} -> ${JSON.stringify(values)}`);
            
            // 将解析出的值按顺序分配给各维度
            crossDimensions.forEach((dimension, index) => {
              if (index < values.length) {
                crossDimensionData[dimension] = values[index];
              }
            });
          } else {
            // 如果没有交叉维度名称字段，尝试直接从字段名匹配
            crossDimensions.forEach(dimension => {
              if (rowData[dimension]) {
                crossDimensionData[dimension] = rowData[dimension];
              }
            });
            
            // 如果还是没有找到，尝试从所有行数据中查找可能的匹配
            if (Object.keys(crossDimensionData).length === 0) {
              console.log('未找到预期的交叉维度字段，尝试从行数据中查找...');
              
              // 尝试通过值来匹配字段
              const possibleMappings = {
                '平台': ['platform', 'Platform', '平台'],
                '省份': ['province', 'Province', '省份'],
                '城市': ['city', 'City', 'standard_city', '城市'],
                '零售商': ['retailer', 'Retailer', 'vender_name', '零售商'],
                '子品牌': ['sub_brand', 'Sub_brand', '子品牌'],
                '商品名称': ['product_name', 'Product_name', 'search_text', '商品名称', '商品'],
                '券机制': ['coupon_mechanism', 'Coupon_mechanism', '券机制'],
                '券门槛': ['coupon_threshold', 'Coupon_threshold', '券门槛'],
                '优惠力度': ['coupon_discount', 'Coupon_discount', '优惠力度']
              };
              
              crossDimensions.forEach(dimension => {
                const possibleFields = possibleMappings[dimension] || [dimension];
                
                for (const field of possibleFields) {
                  if (rowData[field] && rowData[field] !== '' && rowData[field] !== '全部') {
                    crossDimensionData[dimension] = rowData[field];
                    console.log(`找到匹配字段: ${dimension} -> ${field} = ${rowData[field]}`);
                    break;
                  }
                }
              });
            }
          }
        } else if (drillDownPath.length > 0 && drillDownPath[0].dimension && drillDownPath[0].dimension.includes('-')) {
          // 如果是从交叉维度开始的多级下钻，需要从下钻路径的第一层提取交叉维度数据
          const firstLevelPath = drillDownPath[0];
          if (firstLevelPath.crossDimensionData) {
            crossDimensionData = firstLevelPath.crossDimensionData;
          } else {
            // 如果第一层没有保存交叉维度数据，尝试从维度名称中解析
            const crossDimensions = firstLevelPath.dimension.split('-');
            const crossValues = firstLevelPath.value.split('-');
            
            crossDimensions.forEach((dimension, index) => {
              if (index < crossValues.length) {
                crossDimensionData[dimension] = crossValues[index];
              }
            });
          }
        }
        
        console.log('交叉维度下钻 - 当前交叉标签:', currentDimension);
        console.log('交叉维度下钻 - 下钻路径:', drillDownPath);
        console.log('交叉维度下钻 - 行数据:', rowData);
        if (rowData) {
          console.log('交叉维度下钻 - 行数据所有keys:', Object.keys(rowData));
        }
        console.log('交叉维度下钻 - 提取的交叉维度数据:', crossDimensionData);
        
        filters = drillDownManager.buildFiltersForCrossDimension(newPath, crossDimensionData);
        console.log('交叉维度下钻 - 最终筛选条件:', filters);
        console.log('交叉维度下钻 - 维度映射表:', drillDownManager.dimensionApiMap);
      } else {
        filters = drillDownManager.buildFilters(newPath);
      }

      // 准备请求数据
      const requestData = {
        brand: brandFromUrl,
        attr_index: attrIndex,
        target_dimension: targetDimension,
        // 添加对比方式参数
        compare_detail_type: modalCompareDetailType,
        // 直接将筛选条件作为独立参数传递，而不是JSON字符串
        ...filters
      };

      // 添加页面上的筛选条件（如果下钻路径中没有覆盖的话）
      // 这样确保页面上选择的筛选条件会传递到下钻请求中
      if (!requestData.sub_brand && subBrand && Array.isArray(subBrand) && !subBrand.includes('全部')) {
        requestData.sub_brand = subBrand.join(',');
      } else if (!requestData.sub_brand && subBrand && !Array.isArray(subBrand) && subBrand !== '全部') {
        requestData.sub_brand = subBrand;
      }

      if (!requestData.province && province && Array.isArray(province) && !province.includes('全部')) {
        requestData.province = province.join(',');
      } else if (!requestData.province && province && !Array.isArray(province) && province !== '全部') {
        requestData.province = province;
      }

      if (!requestData.standard_city && city && Array.isArray(city) && !city.includes('全部')) {
        requestData.standard_city = city.join(',');
      } else if (!requestData.standard_city && city && !Array.isArray(city) && city !== '全部') {
        requestData.standard_city = city;
      }

      if (!requestData.retailer && retailer && Array.isArray(retailer) && !retailer.includes('全部')) {
        requestData.retailer = retailer.join(',');
      } else if (!requestData.retailer && retailer && !Array.isArray(retailer) && retailer !== '全部') {
        requestData.retailer = retailer;
      }

      if (!requestData.platform && platform && Array.isArray(platform) && !platform.includes('全部')) {
        requestData.platform = platform.join(',');
      } else if (!requestData.platform && platform && !Array.isArray(platform) && platform !== '全部') {
        requestData.platform = platform;
      }

      if (!requestData.upc && selectedProduct && Array.isArray(selectedProduct) && selectedProduct.length > 0) {
        // 对于商品，如果有UPC信息，优先使用UPC
        const upcList = selectedProduct.map(product => {
          if (typeof product === 'object' && product.upc) {
            return product.upc;
          }
          return product;
        }).filter(item => item && item !== '全部');

        if (upcList.length > 0) {
          requestData.upc = upcList.join(',');
        }
      }

      // 活动GMV相关的筛选条件
      if (attrIndex === '活动GMV') {
        if (!requestData.coupon_mechanism && selectedCouponMechanism && Array.isArray(selectedCouponMechanism) && !selectedCouponMechanism.includes('全部')) {
          requestData.coupon_mechanism = selectedCouponMechanism.join(',');
        } else if (!requestData.coupon_mechanism && selectedCouponMechanism && !Array.isArray(selectedCouponMechanism) && selectedCouponMechanism !== '全部') {
          requestData.coupon_mechanism = selectedCouponMechanism;
        }

        if (!requestData.coupon_threshold && selectedCouponThreshold && Array.isArray(selectedCouponThreshold) && !selectedCouponThreshold.includes('全部')) {
          requestData.coupon_threshold = selectedCouponThreshold.join(',');
        } else if (!requestData.coupon_threshold && selectedCouponThreshold && !Array.isArray(selectedCouponThreshold) && selectedCouponThreshold !== '全部') {
          requestData.coupon_threshold = selectedCouponThreshold;
        }

        if (!requestData.coupon_discount && selectedCouponDiscount && Array.isArray(selectedCouponDiscount) && !selectedCouponDiscount.includes('全部')) {
          requestData.coupon_discount = selectedCouponDiscount.join(',');
        } else if (!requestData.coupon_discount && selectedCouponDiscount && !Array.isArray(selectedCouponDiscount) && selectedCouponDiscount !== '全部') {
          requestData.coupon_discount = selectedCouponDiscount;
        }
      }

      // 如果归因设置中选择了指标归因，添加相关参数
      const currentAttributionMethods = showAttributionAnalysis ? lockedAttributionSettings.attributionMethods : attributionMethods;
      const currentIndicatorTypes = showAttributionAnalysis ? lockedAttributionSettings.indicatorType : indicatorType;
      
      if (currentAttributionMethods && currentAttributionMethods.length > 0) {
        requestData.attribution_methods = currentAttributionMethods.join(',');
      }
      
      if (currentIndicatorTypes && currentIndicatorTypes.length > 0) {
        requestData.indicator_types = currentIndicatorTypes.join(',');
      }

      // 添加日期参数
      if (dateType === 'single' && tarDate) {
        if (modalDateType === '月' || modalDateType === '年') {
          if (modalDateType === '月') {
            requestData.tar_start_date = tarDate.startOf('month').format('YYYYMMDD');
            requestData.tar_end_date = tarDate.endOf('month').format('YYYYMMDD');
            if (baseStartDate) {
              requestData.base_start_date = baseStartDate.startOf('month').format('YYYYMMDD');
              requestData.base_end_date = baseStartDate.endOf('month').format('YYYYMMDD');
            }
          } else {
            requestData.tar_start_date = tarDate.startOf('year').format('YYYYMMDD');
            requestData.tar_end_date = tarDate.endOf('year').format('YYYYMMDD');
            if (baseStartDate) {
              requestData.base_start_date = baseStartDate.startOf('year').format('YYYYMMDD');
              requestData.base_end_date = baseStartDate.endOf('year').format('YYYYMMDD');
            }
          }
        } else {
          requestData.tar_date = tarDate.format('YYYYMMDD');
          if (baseStartDate) {
            requestData.base_date = baseStartDate.format('YYYYMMDD');
          }
        }
      } else if (dateType === 'range' && tarStartDate && tarEndDate && baseStartDate && baseEndDate) {
        requestData.tar_start_date = tarStartDate.format('YYYYMMDD');
        requestData.tar_end_date = tarEndDate.format('YYYYMMDD');
        requestData.base_start_date = baseStartDate.format('YYYYMMDD');
        requestData.base_end_date = baseEndDate.format('YYYYMMDD');
      }

      // 发起下钻请求
      console.log('发送到后端的完整请求数据:', requestData);
      console.log('请求参数详细分析:');
      console.log('- brand:', requestData.brand);
      console.log('- attr_index:', requestData.attr_index);
      console.log('- target_dimension:', requestData.target_dimension);
      console.log('- platform:', requestData.platform);
      console.log('- province:', requestData.province);
      console.log('- standard_city:', requestData.standard_city);
      console.log('- retailer:', requestData.retailer);
      console.log('- sub_brand:', requestData.sub_brand);
      console.log('- upc:', requestData.upc);
      console.log('- coupon_mechanism:', requestData.coupon_mechanism);
      console.log('- coupon_threshold:', requestData.coupon_threshold);
      console.log('- coupon_discount:', requestData.coupon_discount);

      // 调试页面筛选条件状态
      console.log('页面筛选条件状态:');
      console.log('- 页面province状态:', province);
      console.log('- 页面city状态:', city);
      console.log('- 页面retailer状态:', retailer);
      console.log('- 页面platform状态:', platform);
      console.log('- 页面selectedProduct状态:', selectedProduct);

      const result = await API.drillDown(requestData);
      
      // 隐藏loading提示
      loadingMessage();
      
      if (result.status === 'success') {
        // 获取下钻数据 - 修复：使用targetDimension而不是result.target_dimension
        const drillData = result.sheets_data[targetDimension] || result.sheets_data[result.target_dimension];
        
        if (drillData && Array.isArray(drillData)) {
          // 缓存下钻数据
          setDrillDownData(prev => ({
            ...prev,
            [drillDownKey]: drillData
          }));
          
          // 更新下钻路径
          setDrillDownPath(newPath);

          // 展开当前行
          const newExpandedRows = new Set(expandedRows);
          newExpandedRows.add(expandedKey);
          setExpandedRows(newExpandedRows);
          
          message.success(`已下钻到${targetDimension}维度`);
        }
      } else {
        message.error('下钻失败：' + result.message);
      }
    } catch (error) {
      // 隐藏loading提示
      loadingMessage();
      
      console.error('下钻失败:', error);
      message.error('下钻失败，请重试');
    }
  }, [drillDownPath, selectedAnalysisDimensions, drillDownData, expandedRows]);

  // 查看指标拆解回调函数
  const handleViewMetrics = useCallback((clickedValue, currentDimension) => {
    console.log('查看指标拆解:', { clickedValue, currentDimension });
    // 这个函数会被 TableContent 组件调用，TableContent 组件内部会处理弹窗的显示
    // 不需要在这里做任何操作，因为 TableContent 组件已经有自己的状态管理
  }, []);

  // 返回上级回调函数
  const handleGoBack = useCallback(() => {
    if (drillDownPath.length > 0) {
      const targetLevel = drillDownPath.length - 1;
      const newPath = drillDownManager.removeDrillLevel(drillDownPath, targetLevel);
      
      // 更新状态
      setDrillDownPath(newPath);
      
      // 清理相关的下钻数据缓存和展开状态
      const newDrillDownData = { ...drillDownData };
      const newExpandedRows = new Set(expandedRows);
      
      // 删除所有与被移除路径相关的缓存数据和展开状态
      Object.keys(newDrillDownData).forEach(key => {
        // 检查缓存键是否包含被移除的路径
        const pathPrefix = drillDownPath.slice(0, targetLevel + 1).map(p => `${p.dimension}:${p.value}`).join('|');
        if (key.includes(pathPrefix) && key !== pathPrefix) {
          delete newDrillDownData[key];
        }
      });
      
      // 清理展开状态
      Array.from(newExpandedRows).forEach(key => {
        const pathPrefix = drillDownPath.slice(0, targetLevel).map(p => `${p.dimension}:${p.value}`).join('|');
        if (key.includes(pathPrefix) && key.split('_').length > (targetLevel + 1) * 2) {
          newExpandedRows.delete(key);
        }
      });
      
      setDrillDownData(newDrillDownData);
      setExpandedRows(newExpandedRows);
      
      // 返回上级时，不改变当前维度标签页
      // currentDimensionTab应该始终反映用户当前查看的维度，而不是下钻路径中的维度
      // 用户可以手动切换标签页来查看不同维度的数据
      
      message.success('已返回上级');
    }
  }, [drillDownPath, drillDownData, expandedRows]);

  // 优化后的面包屑点击处理函数
  const handleBreadcrumbClick = useCallback((level) => {
    if (level === -1) {
      // 点击根级别，重置所有下钻
      handleResetDrillDown();
    } else if (level < drillDownPath.length - 1) {
      // 点击中间级别，返回到指定级别
      const targetLevel = level + 1;
      const newPath = drillDownManager.removeDrillLevel(drillDownPath, targetLevel);
      
      setDrillDownPath(newPath);
      
      // 清理相关的下钻数据缓存和展开状态
      const newDrillDownData = { ...drillDownData };
      const newExpandedRows = new Set(expandedRows);
      
      // 删除所有与被移除路径相关的缓存数据和展开状态
      Object.keys(newDrillDownData).forEach(key => {
        // 检查缓存键是否包含被移除的路径
        const removedPathPrefix = drillDownPath.slice(targetLevel).map(p => `${p.dimension}:${p.value}`).join('|');
        if (removedPathPrefix && key.includes(removedPathPrefix)) {
          delete newDrillDownData[key];
        }
      });
      
      // 清理展开状态
      Array.from(newExpandedRows).forEach(key => {
        const keyParts = key.split('_');
        // 如果展开键的层级深度大于目标层级，则删除
        if (keyParts.length > (targetLevel + 1) * 2) {
          newExpandedRows.delete(key);
        }
      });
      
      setDrillDownData(newDrillDownData);
      setExpandedRows(newExpandedRows);
      
      // 面包屑点击时，不改变当前维度标签页
      // currentDimensionTab应该始终反映用户当前查看的维度，而不是下钻路径中的维度
      
      message.success(`已返回到${newPath.length > 0 ? newPath[newPath.length - 1].displayName : '根级别'}`);
    }
  }, [drillDownPath, drillDownData, expandedRows]);

  // 重置下钻回调函数
  const handleResetDrillDown = useCallback(() => {
    setDrillDownPath([]);
    setDrillDownData({});
    setExpandedRows(new Set());
    
    // 重置时，不改变当前维度标签页
    // 用户可以继续在当前维度标签页查看重置后的数据
    
    message.success('已重置下钻状态');
  }, []);

  // 获取当前显示的数据（包含嵌入的多级下钻数据）
  const getCurrentDisplayData = () => {
    const baseData = Object.keys(sheetsData).length > 0 ? sheetsData : sheets;
    
    // 如果没有下钻数据，直接返回基础数据
    if (Object.keys(drillDownData).length === 0) {
      return baseData;
    }
    
    // 创建一个深拷贝的数据对象，避免修改原始数据
    const enrichedData = JSON.parse(JSON.stringify(baseData));
    
    // 递归嵌入多级下钻数据的函数
    const embedDrillDownData = (rows, currentPath = [], level = 0) => {
      const newRows = [];
      
      rows.forEach((row) => {
        // 添加原始行
        newRows.push({
          ...row,
          _level: level,
          _drillPath: [...currentPath]
        });
        
        // 获取当前行的维度值
        let rowValue;
        const currentDimension = level === 0 ? 
          (drillDownPath.length > 0 ? drillDownPath[0].dimension : Object.keys(enrichedData)[0]) :
          (drillDownPath[level - 1] ? drillDownPath[level - 1].dimension : null);
        
        if (Array.isArray(row)) {
          rowValue = row[0];
        } else if (typeof row === 'object') {
          // 尝试多种方式获取维度值
          rowValue = row[currentDimension] || 
                    row[Object.keys(row)[0]] || 
                    row['name'] || 
                    row['value'];
        }
        
        if (rowValue && currentDimension) {
          // 构建当前行的路径
          const rowPath = [...currentPath, { dimension: currentDimension, value: rowValue }];
          
          // 检查是否有对应的下钻数据
          Object.keys(drillDownData).forEach(drillKey => {
            // 检查缓存键是否匹配当前路径
            const pathString = rowPath.map(p => `${p.dimension}:${p.value}`).join('|');
            
            if (drillKey.startsWith(pathString)) {
              // 构建展开键
              const expandedKey = level === 0 ? 
                `${currentDimension}_${rowValue}` :
                `${currentPath.map(p => `${p.dimension}:${p.value}`).join('|')}_${currentDimension}_${rowValue}`;
              
              if (expandedRows.has(expandedKey)) {
                // 添加下钻数据行
                const drillRows = drillDownData[drillKey];
                if (Array.isArray(drillRows)) {
                  // 递归处理下钻数据，支持多级嵌套
                  const nestedDrillRows = embedDrillDownData(drillRows, rowPath, level + 1);
                  nestedDrillRows.forEach(nestedRow => {
                    newRows.push({
                      ...nestedRow,
                      _isDrillDown: true,
                      _parentValue: rowValue,
                      _level: level + 1,
                      _drillPath: rowPath
                    });
                  });
                }
              }
            }
          });
        }
      });
      
      return newRows;
    };
    
    // 为每个维度的数据嵌入多级下钻数据
    Object.keys(enrichedData).forEach(dimension => {
      const sheetData = enrichedData[dimension];
      
      // 检查数据结构：可能是数组或者有 rows 属性的对象
      let dataArray = null;
      if (Array.isArray(sheetData)) {
        dataArray = sheetData;
      } else if (sheetData && Array.isArray(sheetData.rows)) {
        dataArray = sheetData.rows;
      }
      
      if (dataArray) {
        const enrichedRows = embedDrillDownData(dataArray);
        
        // 更新数据
        if (Array.isArray(sheetData)) {
          enrichedData[dimension] = enrichedRows;
        } else {
          enrichedData[dimension].rows = enrichedRows;
        }
      }
    });
    
    return enrichedData;
  };

  // 初始化归因分析标签页
  useEffect(() => {
    const availableTabs = getAvailableAttributionTabs();
    if (availableTabs.length > 0 && !availableTabs.includes(currentAttributionTab)) {
      const firstTab = availableTabs[0];
      setCurrentAttributionTab(firstTab);
      
      // 根据标签页类型初始化相关状态
      if (firstTab === '维度归因') {
        setCurrentDimensionType('单一维度');
        const dimensionsToUse = showAttributionAnalysis ? lockedAttributionSettings.selectedAnalysisDimensions : selectedAnalysisDimensions;
        if (dimensionsToUse.length > 0) {
          setCurrentDimensionTab(dimensionsToUse[0]);
        }
      }
    }
    
    // 初始化维度标签页
    const dimensionsToUse = showAttributionAnalysis ? lockedAttributionSettings.selectedAnalysisDimensions : selectedAnalysisDimensions;
    if (dimensionsToUse.length > 0 && !dimensionsToUse.includes(currentDimensionTab)) {
      setCurrentDimensionTab(dimensionsToUse[0]);
    }
    
    // 初始化指标标签页
    const indicatorTypesToUse = showAttributionAnalysis ? lockedAttributionSettings.indicatorType : indicatorType;
    if (indicatorTypesToUse.length > 0 && !indicatorTypesToUse.includes(currentIndicatorTab)) {
      setCurrentIndicatorTab(indicatorTypesToUse[0]);
    }
  }, [attributionMethods, selectedAnalysisDimensions, currentAttributionTab, currentDimensionTab, showAttributionAnalysis, lockedAttributionSettings, indicatorType, currentIndicatorTab]);

  // 监听归因指标变化，清理不适用的分析维度选择
  useEffect(() => {
    const availableOptions = getAnalysisDimensionOptions();
    const availableValues = availableOptions.map(option => option.value);
    
    // 过滤掉不再适用的分析维度
    const filteredDimensions = selectedAnalysisDimensions.filter(dimension => 
      availableValues.includes(dimension)
    );
    
    // 如果有维度被过滤掉，更新选择
    if (filteredDimensions.length !== selectedAnalysisDimensions.length) {
      setSelectedAnalysisDimensions(filteredDimensions);
      
      // 如果当前选中的维度标签页不再适用，切换到第一个可用的
      if (filteredDimensions.length > 0 && !filteredDimensions.includes(currentDimensionTab)) {
        setCurrentDimensionTab(filteredDimensions[0]);
      } else if (filteredDimensions.length === 0) {
        setCurrentDimensionTab('');
      }
    }
  }, [attrIndex]);

  // 监听指标类型变化，确保当前选中的指标标签页有效
  useEffect(() => {
    const currentIndicatorTypes = showAttributionAnalysis ? lockedAttributionSettings.indicatorType : indicatorType;
    
    // 如果当前选中的指标标签页不在可用列表中，重置为第一个可用的
    if (currentIndicatorTypes.length > 0 && !currentIndicatorTypes.includes(currentIndicatorTab)) {
      setCurrentIndicatorTab(currentIndicatorTypes[0]);
    } else if (currentIndicatorTypes.length === 0) {
      setCurrentIndicatorTab('');
    }
  }, [indicatorType, showAttributionAnalysis, lockedAttributionSettings, currentIndicatorTab]);

  return (
    <Container fluid className="fade-in" style={{ 
      padding: '20px 20px 0',
      maxWidth: '100%',
      overflow: 'hidden', // 防止容器溢出
      // 确保容器在loading期间仍然允许用户交互
      position: 'relative',
      pointerEvents: 'auto'
    }}>
      {/* 指标设置模块 */}
      <Card className="mb-4">
        <div className="mb-4">
          <h5 style={{ margin: '0 0 16px 0', fontWeight: 'bold' }}>指标设置</h5>
          
          {/* 归因指标 */}
          <div className="d-flex align-items-center mb-4">
            <span style={{ color: '#f5222d', marginRight: '4px' }}>*</span>
            <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0 }}>归因指标</Form.Label>
          <Select
            style={{ width: 200 }}
            value={attrIndex}
            onChange={(value) => setAttrIndex(value)}
            options={[
              { value: '全量GMV', label: '全量GMV' },
              { value: '活动GMV', label: '活动GMV' },
            ]}
          />
        </div>

          {/* 时间对比 */}
          <div className="d-flex align-items-center mb-4">
            <span style={{ color: '#f5222d', marginRight: '4px' }}>*</span>
            <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0 }}>时间对比</Form.Label>
            <Button 
              variant="link" 
              onClick={handleTimeCompareSettingClick}
              style={{ padding: '0', border: 'none', background: 'none', marginRight: '8px',marginBottom: '3px'}}
              title="设置"
            >
              <img 
                src="/logo/image.png" 
                alt="" 
                style={{ 
                  width: '16px', 
                  height: '16px'
                }} 
              />
            </Button>
            <span 
              style={{ 
                fontSize: '14px', 
                color: '#86868b', 
                cursor: 'pointer'
              }}
              onClick={handleTimeCompareSettingClick}
            >
              设置
            </span>
          </div>

          {/* 筛选条件 */}
          <div className="d-flex align-items-center mb-4">
            <span style={{ marginRight: '4px', width: '7px' }}></span>
            <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0 }}>筛选条件</Form.Label>
            <Select
              mode="multiple"
              style={{ width: 200 }}
              placeholder="请选择筛选条件"
              value={selectedFilterConditions}
              onChange={(values) => setSelectedFilterConditions(values)}
              options={
                attrIndex === '全量GMV' 
                  ? [
                      { label: '平台', value: '平台' },
                      { label: '省份', value: '省份' },
                      { label: '城市', value: '城市' },
                      { label: '零售商', value: '零售商' },
                      { label: '子品牌', value: '子品牌' },
                      { label: '商品名称', value: '商品名称' }
                    ]
                  : [
                      { label: '平台', value: '平台' },
                      { label: '省份', value: '省份' },
                      { label: '城市', value: '城市' },
                      { label: '零售商', value: '零售商' },
                      { label: '子品牌', value: '子品牌' },
                      { label: '商品名称', value: '商品名称' },
                      { label: '券机制', value: '券机制' },
                      { label: '券门槛', value: '券门槛' },
                      { label: '优惠力度', value: '优惠力度' }
                    ]
              }
              maxTagCount="responsive"
            />
          </div>

          {/* 根据选中的筛选条件动态显示对应的筛选维度 */}
          <div style={{ 
            display: 'flex', 
            flexWrap: 'wrap',
            gap: '20px',
            marginTop: '0px'
          }}>
            {selectedFilterConditions.includes('平台') && (
            <div className="d-flex align-items-center" style={{ minWidth: '250px' }}>
              <Form.Label className="form-label mb-0 me-2" style={{ whiteSpace: 'nowrap' }}>平台：</Form.Label>
              <Select
                  mode="multiple"
                showSearch
                style={{ width: 200 }}
                placeholder={validateDatesComplete() ? "选择平台" : "请先选择对比日期"}
                optionFilterProp="children"
                onChange={handlePlatformChange}
                value={platform}
                filterOption={filterOption}
                onClick={handlePlatformDropdownClick}
                loading={platformsLoading}
                notFoundContent={platformsLoading ? '加载中...' : (platforms.length <= 1 ? '暂无数据' : null)}
                  maxTagCount="responsive"
                disabled={!validateDatesComplete()}
              >
                {platforms.map(item => (
                  <Select.Option key={item} value={item}>{item}</Select.Option>
                ))}
              </Select>
            </div>
            )}

            {selectedFilterConditions.includes('省份') && (
            <div className="d-flex align-items-center" style={{ minWidth: '250px' }}>
              <Form.Label className="form-label mb-0 me-2" style={{ whiteSpace: 'nowrap' }}>省份：</Form.Label>
              <Select
                  mode="multiple"
                showSearch
                style={{ width: 200 }}
                placeholder={validateDatesComplete() ? "选择省份" : "请先选择对比日期"}
                optionFilterProp="children"
                onChange={handleProvinceChange}
                value={province}
                filterOption={filterOption}
                onClick={handleProvinceDropdownClick}
                loading={provincesLoading}
                notFoundContent={provincesLoading ? '加载中...' : (provinces.length <= 1 ? '暂无数据' : null)}
                  maxTagCount="responsive"
                disabled={!validateDatesComplete()}
              >
                {provinces.map(item => (
                  <Select.Option key={item} value={item}>{item}</Select.Option>
                ))}
              </Select>
            </div>
            )}

            {selectedFilterConditions.includes('城市') && (
            <div className="d-flex align-items-center" style={{ minWidth: '250px' }}>
              <Form.Label className="form-label mb-0 me-2" style={{ whiteSpace: 'nowrap' }}>城市：</Form.Label>
              <Select
                  mode="multiple"
                showSearch
                style={{ width: 200 }}
                placeholder={validateDatesComplete() ? "选择城市" : "请先选择对比日期"}
                optionFilterProp="children"
                onChange={handleCityChange}
                value={city}
                filterOption={filterOption}
                onClick={handleCityDropdownClick}
                loading={citiesLoading}
                notFoundContent={citiesLoading ? '加载中...' : (cities.length <= 1 ? '暂无数据' : null)}
                  maxTagCount="responsive"
                disabled={!validateDatesComplete()}
              >
                {cities.map(item => (
                  <Select.Option key={item} value={item}>{item}</Select.Option>
                ))}
              </Select>
            </div>
            )}

            {selectedFilterConditions.includes('零售商') && (
            <div className="d-flex align-items-center" style={{ minWidth: '280px' }}>
              <Form.Label className="form-label mb-0 me-2" style={{ whiteSpace: 'nowrap' }}>零售商：</Form.Label>
              <Select
                  mode="multiple"
                showSearch
                style={{ width: 200 }}
                placeholder={validateDatesComplete() ? "选择零售商" : "请先选择对比日期"}
                optionFilterProp="children"
                onChange={handleRetailerChange}
                value={retailer}
                filterOption={filterOption}
                onClick={handleRetailerDropdownClick}
                loading={retailersLoading}
                notFoundContent={retailersLoading ? '加载中...' : (retailers.length <= 1 ? '暂无数据' : null)}
                  maxTagCount="responsive"
                disabled={!validateDatesComplete()}
              >
                {retailers.map(item => (
                  <Select.Option key={item} value={item}>{item}</Select.Option>
                ))}
              </Select>
            </div>
            )}

            {selectedFilterConditions.includes('子品牌') && (
            <div className="d-flex align-items-center">
              <Form.Label className="form-label mb-0 me-2">子品牌：</Form.Label>
              <Select
                  mode="multiple"
                showSearch
                style={{ width: 200 }}
                placeholder={validateDatesComplete() ? "选择子品牌" : "请先选择对比日期"}
                optionFilterProp="children"
                onChange={handleSubBrandChange}
                value={subBrand}
                filterOption={filterOption}
                onClick={handleSubBrandDropdownClick}
                loading={subBrandsLoading}
                notFoundContent={subBrandsLoading ? '加载中...' : (subBrands.length <= 1 ? '暂无数据' : null)}
                  maxTagCount="responsive"
                disabled={!validateDatesComplete()}
              >
                {subBrands.map(item => (
                  <Select.Option key={item} value={item}>{item}</Select.Option>
                ))}
              </Select>
            </div>
            )}

            {(selectedFilterConditions.includes('商品名称') || selectedFilterConditions.includes('UPC/商品名称')) && (
            <div className="d-flex align-items-center">
              <Form.Label className="form-label mb-0 me-2">UPC/商品名称：</Form.Label>
              <Select
                  mode="multiple"
                showSearch
                style={{ width: 320 }}
                placeholder={validateDatesComplete() ? "搜索UPC或商品名称" : "请先选择对比日期"}
                optionFilterProp="children"
                onChange={handleSearchTextChange}
                  value={selectedProduct}
                filterOption={(input, option) => {
                  const data = option?.['data-item'];
                  if (!data || !input) return false;
                  
                  const productName = data.product_name || '';
                  const upc = data.upc || '';
                  const inputLower = input.toLowerCase();
                  
                  return (
                    productName.toLowerCase().indexOf(inputLower) >= 0 || 
                    upc.toLowerCase().indexOf(inputLower) >= 0
                  );
                }}
                onClick={handleProductDropdownClick}
                loading={productsLoading}
                notFoundContent={productsLoading ? '加载中...' : (products.length <= 1 ? '暂无数据' : null)}
                labelInValue={false}
                optionLabelProp="label"
                  maxTagCount="responsive"
                disabled={!validateDatesComplete()}
              >
                {products.map(item => (
                  <Select.Option 
                    key={item.upc || 'all'} 
                    value={item.product_name}
                    data-item={item}
                    label={
                      item.product_name === '全部' ? '全部' : 
                      `${item.product_name} ${item.upc ? `UPC: ${item.upc}` : ''}`
                    }
                  >
                    {item.product_name === '全部' ? '全部' : (
                      <div>
                        <div>{item.product_name}</div>
                        <div style={{ fontSize: '12px', color: '#86868b' }}>UPC: {item.upc}</div>
                      </div>
                    )}
                  </Select.Option>
                ))}
              </Select>
            </div>
            )}
          </div>

          {/* 券相关筛选维度 */}
          {showCouponFilters && (
            <div style={{ 
              display: 'flex', 
              gap: '20px', 
              marginTop: '20px'
            }}>
              {selectedFilterConditions.includes('券机制') && (
              <div className="d-flex align-items-center">
                <Form.Label className="form-label mb-0 me-2">券机制：</Form.Label>
                <Select
                    mode="multiple"
                  showSearch
                  style={{ width: 200 }}
                  placeholder={validateDatesComplete() ? "选择券机制" : "请先选择对比日期"}
                  optionFilterProp="children"
                  onChange={handleCouponMechanismChange}
                  value={selectedCouponMechanism}
                  filterOption={filterOption}
                  onClick={handleCouponMechanismDropdownClick}
                  loading={couponMechanismsLoading}
                  notFoundContent={couponMechanismsLoading ? '加载中...' : (couponMechanisms.length <= 1 ? '暂无数据' : null)}
                    maxTagCount="responsive"
                  disabled={!validateDatesComplete()}
                >
                  {couponMechanisms.map(item => (
                    <Select.Option key={item} value={item}>{item}</Select.Option>
                  ))}
                </Select>
              </div>
              )}

              {selectedFilterConditions.includes('券门槛') && (
              <div className="d-flex align-items-center">
                <Form.Label className="form-label mb-0 me-2">券门槛：</Form.Label>
                <Select
                    mode="multiple"
                  showSearch
                  style={{ width: 200 }}
                  placeholder={validateDatesComplete() ? "选择券门槛" : "请先选择对比日期"}
                  optionFilterProp="children"
                  onChange={handleCouponThresholdChange}
                  value={selectedCouponThreshold}
                  filterOption={filterOption}
                  onClick={handleCouponThresholdDropdownClick}
                  loading={couponThresholdsLoading}
                  notFoundContent={couponThresholdsLoading ? '加载中...' : (couponThresholds.length <= 1 ? '暂无数据' : null)}
                    maxTagCount="responsive"
                  disabled={!validateDatesComplete()}
                >
                  {couponThresholds.map(item => (
                    <Select.Option key={item} value={item}>{item}</Select.Option>
                  ))}
                </Select>
              </div>
              )}

              {selectedFilterConditions.includes('优惠力度') && (
              <div className="d-flex align-items-center">
                <Form.Label className="form-label mb-0 me-2">优惠力度：</Form.Label>
                <Select
                    mode="multiple"
                  showSearch
                  style={{ width: 200 }}
                  placeholder={validateDatesComplete() ? "选择优惠力度" : "请先选择对比日期"}
                  optionFilterProp="children"
                  onChange={handleCouponDiscountChange}
                  value={selectedCouponDiscount}
                  filterOption={filterOption}
                  onClick={handleCouponDiscountDropdownClick}
                  loading={couponDiscountsLoading}
                  notFoundContent={couponDiscountsLoading ? '加载中...' : (couponDiscounts.length <= 1 ? '暂无数据' : null)}
                    maxTagCount="responsive"
                  disabled={!validateDatesComplete()}
                >
                  {couponDiscounts.map(item => (
                    <Select.Option key={item} value={item}>{item}</Select.Option>
                  ))}
                </Select>
              </div>
              )}
            </div>
          )}

          {/* 分隔线 */}
          <div style={{ 
            width: '100%', 
            height: '1px', 
            backgroundColor: '#d9d9d9', 
            margin: '24px 0 20px 0' 
          }}></div>

          {/* 归因设置 */}
          <div className="d-flex align-items-center mb-4">
            <span style={{ color: '#f5222d', marginRight: '4px' }}>*</span>
            <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0 }}>归因设置</Form.Label>
            <Button 
              variant="link" 
              onClick={handleAttributionSettingClick}
              style={{ padding: '0', border: 'none', background: 'none', marginRight: '8px', marginBottom: '3px'}}
              title="设置"
            >
              <img 
                src="/logo/image.png" 
                alt="" 
                style={{ 
                  width: '16px', 
                  height: '16px'
                }} 
              />
            </Button>
            <span 
              style={{ 
                fontSize: '14px', 
                color: '#86868b', 
                cursor: 'pointer'
              }}
              onClick={handleAttributionSettingClick}
            >
              设置
            </span>
          </div>
        </div>
      </Card>

      {/* 时间对比弹框 */}
      <Modal
        title="时间对比"
        open={timeCompareModalVisible}
        onOk={handleTimeCompareModalOk}
        onCancel={handleTimeCompareModalCancel}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ padding: '20px 0' }}>
          {/* 日期类型 */}
          <div className="mb-4">
            <div className="d-flex align-items-center mb-2">
              <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0, width: '80px' }}>日期类型</Form.Label>
              <Select
                style={{ width: 300 }}
                value={modalDateType}
                onChange={(value) => setModalDateType(value)}
                options={[
                  { value: '日', label: '日' },
                  { value: '月', label: '月' },
                  { value: '年', label: '年' },
                ]}
              />
            </div>
          </div>

          {/* 目标日期类型 */}
          <div className="mb-4">
            <div className="d-flex align-items-center mb-2">
              <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0, width: '80px' }}>目标日期</Form.Label>
              <Select
                style={{ width: 300 }}
                value={modalTargetDateType}
                onChange={(value) => setModalTargetDateType(value)}
                options={[
                  { value: '指定日期', label: '指定日期' },
                  { value: '日期范围', label: '日期范围' },
                ]}
              />
            </div>
          </div>

          {/* 根据目标日期类型显示不同的日期选择器 */}
          {modalTargetDateType === '指定日期' ? (
            <div className="mb-4">
              <div className="d-flex align-items-center mb-2">
                <div style={{ width: '96px' }}></div>
                <DatePicker 
                  locale={locale}
                  picker={getPickerType(modalDateType)}
                  value={tarDate}
                  onChange={handleTarDateChangeWithCompare}
                  placeholder={getPlaceholderByType(modalDateType)}
                  disabledDate={getDisabledDateByType(modalDateType)}
                  format={getDateFormat(modalDateType)}
                  style={{ width: 300 }}
                />
              </div>
            </div>
          ) : (
            <div className="mb-4">
              <div className="d-flex align-items-center mb-2">
                <div style={{ width: '96px' }}></div>
                <Space>
                  <DatePicker 
                    locale={locale}
                    picker={getPickerType(modalDateType)}
                    value={tarStartDate}
                    onChange={handleTarStartDateChangeWithCompare}
                    placeholder={getPlaceholderByType(modalDateType, true)[0]}
                    disabledDate={getDisabledDateByType(modalDateType)}
                    format={getDateFormat(modalDateType)}
                    style={{ width: 145 }}
                  />
                  <DatePicker 
                    locale={locale}
                    picker={getPickerType(modalDateType)}
                    value={tarEndDate}
                    onChange={handleTarEndDateChangeWithCompare}
                    placeholder={getPlaceholderByType(modalDateType, true)[1]}
                    disabledDate={getDisabledDateByType(modalDateType)}
                    format={getDateFormat(modalDateType)}
                    style={{ width: 145 }}
                  />
                </Space>
              </div>
            </div>
          )}

          {/* 对比方式 */}
          <div className="mb-4">
            <div className="d-flex align-items-center mb-2">
              <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0, width: '80px' }}>对比方式</Form.Label>
              <Space>
                <Select
                  style={{ width: modalCompareMethod === '自定义区间' ? 300 : 145 }}
                  value={modalCompareMethod}
                  onChange={(value) => setModalCompareMethod(value)}
                  options={[
                    { value: '同环比', label: '同环比' },
                    { value: '自定义区间', label: '自定义区间' },
                  ]}
                />
                {modalCompareMethod === '同环比' && (
                  <Select
                    style={{ width: 145 }}
                    value={modalCompareDetailType}
                    onChange={(value) => setModalCompareDetailType(value)}
                    options={getCompareDetailOptions(modalDateType)}
                  />
                )}
              </Space>
            </div>
          </div>

          {/* 只有"自定义区间"时才显示对比日期相关选择器 */}
          {modalCompareMethod === '自定义区间' && (
            <>
              {/* 对比日期类型 */}
              <div className="mb-4">
                <div className="d-flex align-items-center mb-2">
                  <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0, width: '80px' }}>对比日期</Form.Label>
                  <Select
                    style={{ width: 300 }}
                    value={modalStartDateType}
                    onChange={(value) => setModalStartDateType(value)}
                    options={[
                      { value: '指定日期', label: '指定日期' },
                      { value: '日期范围', label: '日期范围' },
                    ]}
                  />
                </div>
              </div>

              {/* 根据对比日期类型显示不同的日期选择器 */}
              {modalStartDateType === '指定日期' ? (
                <div className="mb-4">
                  <div className="d-flex align-items-center mb-2">
                    <div style={{ width: '96px' }}></div>
                    <DatePicker
                      locale={locale}
                      picker={getPickerType(modalDateType)}
                      value={baseStartDate}
                      onChange={(date) => setBaseStartDate(date)}
                      placeholder={getPlaceholderByType(modalDateType)}
                      disabledDate={getDisabledDateByType(modalDateType)}
                      format={getDateFormat(modalDateType)}
                      style={{ width: 300 }}
                    />
                  </div>
                </div>
              ) : (
                <div className="mb-4">
                  <div className="d-flex align-items-center mb-2">
                    <div style={{ width: '96px' }}></div>
                    <Space>
                      <DatePicker
                        locale={locale}
                        picker={getPickerType(modalDateType)}
                        value={baseStartDate}
                        onChange={(date) => setBaseStartDate(date)}
                        placeholder={getPlaceholderByType(modalDateType, true)[0]}
                        disabledDate={getDisabledDateByType(modalDateType)}
                        format={getDateFormat(modalDateType)}
                        style={{ width: 145 }}
                      />
                      <DatePicker
                        locale={locale}
                        picker={getPickerType(modalDateType)}
                        value={baseEndDate}
                        onChange={(date) => setBaseEndDate(date)}
                        placeholder={getPlaceholderByType(modalDateType, true)[1]}
                        disabledDate={getDisabledDateByType(modalDateType)}
                        format={getDateFormat(modalDateType)}
                        style={{ width: 145 }}
                      />
                    </Space>
                  </div>
                </div>
              )}
            </>
          )}

          {/* 日期范围显示 */}
          <div className="mb-4" style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            <div style={{ display: 'flex', justifyContent: 'center', gap: '150px' }}>
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: '4px', textAlign: 'center' }}>目标日期</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  {modalTargetDateType === '指定日期' 
                    ? (tarDate ? tarDate.format(getDateFormat(modalDateType)) : '未选择')
                    : (tarStartDate && tarEndDate 
                      ? `${tarStartDate.format(getDateFormat(modalDateType))} - ${tarEndDate.format(getDateFormat(modalDateType))}` 
                      : '未选择')
                  }
                </div>
              </div>
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: '4px', textAlign: 'center' }}>对比日期</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  {modalStartDateType === '指定日期' || (modalCompareMethod === '同环比' && modalTargetDateType === '指定日期')
                    ? (baseStartDate ? baseStartDate.format(getDateFormat(modalDateType)) : '未选择')
                    : (baseStartDate && baseEndDate 
                      ? `${baseStartDate.format(getDateFormat(modalDateType))} - ${baseEndDate.format(getDateFormat(modalDateType))}` 
                      : '未选择')
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>

      {/* 归因设置弹框 */}
      <Modal
        title="归因设置"
        open={attributionModalVisible}
        onOk={handleAttributionModalOk}
        onCancel={handleAttributionModalCancel}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ padding: '20px 0' }}>
          {/* 归因方式 */}
          <div className="mb-4">
            <div className="d-flex align-items-center mb-2">
              <span style={{ color: '#f5222d', marginRight: '4px' }}>*</span>
              <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0, width: '80px' }}>归因方式</Form.Label>
              <Space>
                <Checkbox
                  checked={attributionMethods.includes('维度')}
                  onChange={(e) => {
                    if (e.target.checked) {
                      handleAttributionMethodsChange([...attributionMethods, '维度']);
                    } else {
                      handleAttributionMethodsChange(attributionMethods.filter(method => method !== '维度'));
                    }
                  }}
                >
                  维度
                </Checkbox>
                <Checkbox
                  checked={attributionMethods.includes('指标')}
                  onChange={(e) => {
                    if (e.target.checked) {
                      handleAttributionMethodsChange([...attributionMethods, '指标']);
                    } else {
                      handleAttributionMethodsChange(attributionMethods.filter(method => method !== '指标'));
                    }
                  }}
                >
                  指标
                </Checkbox>

              </Space>
            </div>
          </div>

          {/* 标签页内容 */}
          <div style={{ marginTop: '20px' }}>
            {/* 标签页导航 - 只有在有选择归因方式时才显示 */}
            {attributionMethods.length > 0 && (
              <div style={{ borderBottom: '1px solid #d9d9d9', marginBottom: '20px' }}>
                {attributionMethods.map(method => (
                  <AntButton
                    key={method}
                    type={activeAttributionTab === method ? 'primary' : 'text'}
                    onClick={() => setActiveAttributionTab(method)}
                    style={{ 
                      marginRight: '16px',
                      marginBottom: '8px',
                      border: 'none',
                      borderBottom: activeAttributionTab === method ? '2px solid #1890ff' : 'none',
                      borderRadius: 0,
                      background: 'none',
                      color: activeAttributionTab === method ? '#1890ff' : '#666'
                    }}
                  >
                    {method}归因
                  </AntButton>
                ))}
              </div>
            )}

            {/* 当没有选择任何归因方式时显示提示 */}
            {attributionMethods.length === 0 && (
              <div style={{ padding: '40px 20px', textAlign: 'center', color: '#999' }}>
                请先选择归因方式
              </div>
            )}

            {/* 维度归因标签页内容 */}
            {activeAttributionTab === '维度' && attributionMethods.includes('维度') && (
              <div>
                <div className="d-flex align-items-center mb-3">
                  <span style={{ color: '#f5222d', marginRight: '4px' }}>*</span>
                  <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0, width: '80px' }}>分析维度</Form.Label>
                  <Select
                    mode="multiple"
                    style={{ width: 300 }}
                    placeholder="选择分析维度"
                    value={selectedAnalysisDimensions}
                    onChange={handleAnalysisDimensionsChange}
                    options={getAnalysisDimensionOptions()}
                    maxTagCount="responsive"
                  />
                </div>
                
                {/* 交叉维度归因复选框 */}
                <div className="d-flex align-items-center mb-3">
                  <div style={{ width: '96px' }}></div>
                  <Checkbox
                    checked={crossDimensionEnabled}
                    onChange={(e) => handleCrossDimensionChange(e.target.checked)}
                  >
                    交叉维度归因
                  </Checkbox>
                </div>

                {/* 交叉维度归因组合 */}
                {crossDimensionEnabled && (
                  <div style={{ marginLeft: '96px' }}>
                    {dimensionCombinations.map((combination, index) => (
                      <div key={combination.id} style={{ marginBottom: '16px' }}>
                        <div className="d-flex align-items-center mb-2">
                          <span style={{ marginRight: '16px', minWidth: '60px' }}>组合{index + 1}</span>
                          <Select
                            mode="multiple"
                            style={{ width: 300, marginRight: '8px' }}
                            placeholder="选择维度组合"
                            value={combination.dimensions}
                            onChange={(dimensions) => updateCombinationDimensions(combination.id, dimensions)}
                            options={getAnalysisDimensionOptions().filter(option => 
                              selectedAnalysisDimensions.includes(option.value)
                            )}
                            maxTagCount="responsive"
                          />
                          {dimensionCombinations.length > 1 && (
                            <AntButton
                              type="text"
                              icon={<DeleteOutlined />}
                              onClick={() => removeDimensionCombination(combination.id)}
                              style={{ color: '#ff4d4f' }}
                            />
                          )}
                        </div>
                      </div>
                    ))}
                    
                    {/* 添加组合按钮 */}
                    <div>
                      <AntButton
                        type="dashed"
                        onClick={addDimensionCombination}
                        style={{ width: '300px' }}
                      >
                        + 添加组合
                      </AntButton>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 指标归因标签页内容 */}
            {activeAttributionTab === '指标' && attributionMethods.includes('指标') && (
              <div>
                <div className="d-flex align-items-center mb-3">
                  <span style={{ color: '#f5222d', marginRight: '4px' }}>*</span>
                  <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0, width: '80px' }}>指标类型</Form.Label>
                  <Select
                    mode="multiple"
                    style={{ width: 300 }}
                    value={indicatorType}
                    onChange={handleIndicatorTypeChange}
                    placeholder="选择指标类型"
                    options={[
                      { 
                        label: '供给向', 
                        value: '供给向',
                        disabled: attrIndex === '活动GMV' || !modalCompareDetailType || !(modalDateType === '月' || modalCompareDetailType.includes('月'))
                      },
                      { label: '营销向', value: '营销向' }
                    ]}
                    maxTagCount="responsive"
                  />
                </div>

                {/* 指标关系 */}
                <div className="d-flex align-items-start mb-3">
                  <span style={{ color: '#f5222d', marginRight: '4px' }}>*</span>
                  <Form.Label className="form-label" style={{ marginRight: '16px', marginBottom: 0, width: '80px' }}>指标关系</Form.Label>
                  <div style={{ width: 300 }}>
                    {indicatorType.length === 0 ? (
                      <div style={{ 
                        padding: '8px 12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px',
                        color: '#999',
                        fontSize: '14px'
                      }}>
                        请选择指标类型
                      </div>
                    ) : (
                      <div>
                        {indicatorType.map((type, typeIndex) => (
                          <div key={type} style={{ marginBottom: typeIndex < indicatorType.length - 1 ? '20px' : '0' }}>
                            <div style={{ 
                              fontWeight: 'bold', 
                              marginBottom: '8px', 
                              color: '#1890ff',
                              fontSize: '14px'
                            }}>
                              {type}指标关系：
                            </div>
                            {getIndicatorFormulas(type).map((formula, formulaIndex) => (
                              <div key={formulaIndex} style={{ 
                                marginBottom: '4px', 
                                padding: '8px 12px', 
                                backgroundColor: '#f5f5f5', 
                                borderRadius: '4px',
                                fontSize: '14px',
                                color: '#333'
                              }}>
                                {formula}
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

              </div>
            )}


          </div>
        </div>
      </Modal>

      <div className="mb-4">
        <div className="d-flex align-items-center gap-3 mb-4">
          <AntButton 
            type="primary" 
            onClick={fetchTopRanking}
            loading={isLoadingTopRanking}
            style={{ minWidth: '120px' }}
          >
            一键归因
          </AntButton>
          
          {isLoadingTopRanking && (
            <div style={{ flex: 1 }}>
              <ProgressBar
                animated
                now={100}
                style={{ height: '6px' }}
              />
            </div>
          )}
        </div>
        
        {rankingMarketData && (
          <Card className="mb-4" style={{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }}>
            <div style={{ padding: '10px 15px' }}>
              <h5 style={{ margin: '0 0 20px 0', padding: 0 }}>指标异动解读</h5>
              
              {/* 两个大卡片显示GMV数据 */}
              <div style={{ display: 'flex', gap: '20px', marginBottom: '30px' }}>
                {/* 目标日期GMV卡片 */}
                <div style={{
                  flex: 1,
                  padding: '24px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '12px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                }}>
                  <div style={{ 
                    fontSize: '16px', 
                    fontWeight: 'bold', 
                    color: '#495057', 
                    marginBottom: '16px'
                  }}>
                    {attrIndex}
                  </div>
                  <div style={{ 
                    fontSize: '14px', 
                    color: '#6c757d',
                    marginBottom: '20px',
                    height: '20px' // 固定高度确保对齐
                  }}>
                    目标日期：{dateType === 'single' 
                      ? formatDateDisplay(tarDate, modalDateType)
                      : (tarStartDate && tarEndDate 
                        ? `${formatDateDisplay(tarStartDate, modalDateType)} - ${formatDateDisplay(tarEndDate, modalDateType)}`
                        : '未选择')
                    }
                  </div>
                  <div style={{ 
                    fontSize: '32px', 
                    fontWeight: 'bold', 
                    color: '#000000',
                    marginBottom: '20px'
                  }}>
                    {rankingMarketData.gmv_target?.toLocaleString()} 元
                  </div>
                  <div style={{ 
                    display: 'flex', 
                    gap: '20px' 
                  }}>
                    <div>
                      <span style={{ 
                        fontSize: '12px', 
                        color: '#6c757d'
                      }}>
                        变化值
                      </span>
                      <span style={{ 
                        fontSize: '16px', 
                        fontWeight: 'bold',
                        color: parseFloat(rankingMarketData.gmv_change) >= 0 ? '#52c41a' : '#f5222d',
                        marginLeft: '8px'
                      }}>
                        {parseFloat(rankingMarketData.gmv_change) >= 0 ? '+' : ''}{rankingMarketData.gmv_change?.toLocaleString()} 元
                      </span>
                    </div>
                    <div>
                      <span style={{ 
                        fontSize: '12px', 
                        color: '#6c757d'
                      }}>
                        变化率
                      </span>
                      <span style={{ 
                        fontSize: '16px', 
                        fontWeight: 'bold',
                        color: parseFloat(rankingMarketData.gmv_change_rate) >= 0 ? '#52c41a' : '#f5222d',
                        marginLeft: '8px'
                      }}>
                        {parseFloat(rankingMarketData.gmv_change_rate) >= 0 ? '+' : ''}{parseFloat(rankingMarketData.gmv_change_rate).toFixed(2)}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* 对比日期GMV卡片 */}
                <div style={{
                  flex: 1,
                  padding: '24px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '12px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                }}>
                  <div style={{ 
                    fontSize: '16px', 
                    fontWeight: 'bold', 
                    color: '#495057', 
                    marginBottom: '16px'
                  }}>
                    {/* 空白区域保持对齐 */}
                    &nbsp;
                  </div>
                  <div style={{ 
                    fontSize: '14px', 
                    color: '#6c757d',
                    marginBottom: '20px',
                    height: '20px' // 固定高度确保对齐
                  }}>
                    对比日期：{dateType === 'single' 
                      ? formatDateDisplay(baseStartDate, modalDateType)
                      : (baseStartDate && baseEndDate 
                        ? `${formatDateDisplay(baseStartDate, modalDateType)} - ${formatDateDisplay(baseEndDate, modalDateType)}`
                        : '未选择')
                    }
                  </div>
                  <div style={{ 
                    fontSize: '32px', 
                    fontWeight: 'bold', 
                    color: '#000000',
                    marginBottom: '20px'
                  }}>
                    {rankingMarketData.gmv_base?.toLocaleString()} 元
                  </div>
                  {/* 添加空白区域保持高度一致 */}
                  <div style={{ height: '44px' }}></div>
                </div>
              </div>

              {/* 移除原来的变化值和变化率独立显示区域 */}

              {/* 历史GMV变化图表 */}
              {historicalGMVChanges && historicalGMVChanges.length > 0 && (
                <div className="historical-gmv-changes" style={{ marginBottom: '20px' }}>
                  {(() => {
                    const anomalyInfo = analyzeGMVAnomaly(
                      historicalGMVChanges, 
                      rankingMarketData.gmv_change, 
                      rankingMarketData.gmv_change_rate
                    );
                    
                    return (
                      <div>
                      <div style={{ height: '300px' }}>
                          {(() => {
                            // 判断是否应该显示日期范围图表（两条红线）
                            const shouldShowRangeChart = 
                              // 情况1：明确的日期范围模式
                              dateType === 'range' ||
                              // 情况2：指定日期模式但日期类型为月或年（实际上是日期范围）
                              (dateType === 'single' && (modalDateType === '月' || modalDateType === '年'));
                            
                            return (
                        <ReactECharts 
                                option={shouldShowRangeChart
                                  ? getGMVChartOptionForRange(historicalGMVChanges, anomalyInfo)
                                  : getGMVChartOption(historicalGMVChanges, anomalyInfo)
                          } 
                          style={{ height: '100%', width: '100%' }}
                        />
                            );
                          })()}
                        </div>
                        {/* 日期范围提示 */}
                        {dateType === 'single' && (
                          <div style={{ 
                            fontSize: '12px', 
                            color: '#666', 
                            marginTop: '8px', 
                            textAlign: 'center' 
                          }}>
                            提示：如需显示目标日期范围的两条标记线，请在时间对比设置中选择"日期范围"模式
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              )}
              
              {/* 指标异动解读文字 */}
              {historicalGMVChanges && historicalGMVChanges.length > 0 && (
                <div className="historical-gmv-changes" style={{ marginTop: '20px' }}>
                  {(() => {
                    const anomalyInfo = analyzeGMVAnomaly(
                      historicalGMVChanges, 
                      rankingMarketData.gmv_change, 
                      rankingMarketData.gmv_change_rate
                    );
                    
                    // 确定方向词和颜色
                    const direction = rankingMarketData.direction || (parseFloat(rankingMarketData.gmv_change_rate) >= 0 ? '增长' : '下降');
                    const colorStyle = direction === '增长' || direction === '上升' ? '#52c41a' : '#f5222d';
                    
                    // 构建历史比较部分
                    const historyCompare = anomalyInfo.mean !== undefined ? 
                      `${parseFloat(rankingMarketData.gmv_change_rate) > anomalyInfo.mean ? '高于' : '低于'}历史平均GMV变化率${anomalyInfo.mean.toFixed(2)}%` : '';
                    
                    return (
                      <div style={{ 
                        lineHeight: '1.8', 
                        padding: '16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '8px',
                        fontSize: '14px',
                        color: '#495057'
                      }}>
                        <div>
                          当前GMV {rankingMarketData.gmv_target?.toLocaleString()} 元，基准GMV {rankingMarketData.gmv_base?.toLocaleString()} 元，
                          GMV变化值 {rankingMarketData.gmv_change?.toLocaleString()} 元，
                          <span style={{ color: colorStyle, fontWeight: 'bold' }}>
                            GMV变化率 {parseFloat(rankingMarketData.gmv_change_rate).toFixed(2)}% ({direction})
                          </span>，
                          {historyCompare}，{anomalyInfo.isAnomaly ? '出现异常波动' : '未出现明显异常波动'}。
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
              
              {/* 如果没有历史数据，只显示基本信息 */}
              {(!historicalGMVChanges || historicalGMVChanges.length === 0) && (
                <div style={{ 
                  padding: '16px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '8px',
                  fontSize: '14px',
                  color: '#495057',
                  lineHeight: '1.8'
                }}>
                  <div>
                    <span style={{ fontWeight: 'bold' }}>指标解读：</span>
                    当前GMV {rankingMarketData.gmv_target?.toLocaleString()} 元，基准GMV {rankingMarketData.gmv_base?.toLocaleString()} 元，
                    GMV变化值 {rankingMarketData.gmv_change?.toLocaleString()} 元，
                    <span style={{ 
                      color: rankingMarketData.direction === '上升' || rankingMarketData.direction === '增长' || parseFloat(rankingMarketData.gmv_change_rate) >= 0 ? '#52c41a' : '#f5222d',
                      fontWeight: 'bold'
                    }}>
                      GMV变化率 {parseFloat(rankingMarketData.gmv_change_rate).toFixed(2)}% ({rankingMarketData.direction || (parseFloat(rankingMarketData.gmv_change_rate) >= 0 ? '增长' : '下降')})
                    </span>
                  </div>
                </div>
              )}
            </div>
          </Card>
        )}
        
        {/* 只有在有数据时才显示维度下钻部分 - 优化：数据获取后立即显示，不等待AI归因分析 */}
        {(Object.keys(sheetsData).length > 0 || Object.keys(sheets).length > 0 || rankingMarketData) && (
          <Card 
            className="mb-4"
            style={{
              // 确保表格在AI归因loading期间不被阻塞
              position: 'relative',
              zIndex: 2,
              pointerEvents: 'auto'
            }}
          >
            <div className="d-flex justify-content-between align-items-start mb-3" style={{ padding: '1.25rem 1.25rem 0' }}>
              {/* 标题和提示信息 */}
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: '32px' }}>
                <h5 style={{ margin: '0 0 8px 0', padding: 0, whiteSpace: 'nowrap' }}>归因分析</h5>
                <div style={{
                  fontSize: '12px',
                  color: '#666',
                  lineHeight: '1.4',
                  maxWidth: '1400px',
                  marginRight: '50px',
                  marginTop: '2px'
                }}>
                  贡献度：某维度数据的波动对整体指标波动的贡献比例，
                  其中当大盘波动为正向时，各项贡献度之和为+100%；
                  当大盘波动为负向时，各项贡献度之和为-100%，
                  反映其对整体波动的相对影响程度。
                </div>
              </div>
              {(Object.keys(sheetsData).length > 0 || Object.keys(sheets).length > 0) && (
                <AntButton type="primary" onClick={() => API.downloadExcel(filePath)} style={{ whiteSpace: 'nowrap' }}>
                  下载完整结果
                </AntButton>
              )}
            </div>
            
            {/* 归因分析主标签页 */}
            {getAvailableAttributionTabs().length > 0 && (
              <div style={{ padding: '0 1.25rem' }}>
                {/* 使用Ant Design Tabs组件，卡片式样式 */}
                <Tabs
                  type="card"
                  activeKey={currentAttributionTab}
                  onChange={handleAttributionTabChange}
                  style={{ marginBottom: '20px' }}
                  items={getAvailableAttributionTabs().map(tab => ({
                    key: tab,
                    label: tab,
                    children: (
                      <div>
                        {/* 归因分析内容区域 */}
                        <div style={{ display: 'flex', gap: '20px', minHeight: '400px' }}>
                          {/* 左侧筛选区域 */}
                          {tab === '维度归因' && (
                            <div style={{ 
                              width: '150px', 
                              borderRight: '1px solid #f0f0f0', 
                              paddingRight: '20px',
                              flexShrink: 0
                            }}>
                                                                      {/* 维度类型标签页 - 改为上下排列 */}
                                        <div style={{ marginBottom: '20px' }}>
                                          <div style={{ marginBottom: '12px', fontWeight: 'bold', fontSize: '14px' }}>维度类型</div>
                                          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                                            <AntButton
                                              type={currentDimensionType === '单一维度' ? 'primary' : 'text'}
                                              onClick={() => handleDimensionTypeChange('单一维度')}
                                              style={{ 
                                                textAlign: 'left',
                                                border: 'none',
                                                background: currentDimensionType === '单一维度' ? '#e6f7ff' : 'transparent',
                                                color: currentDimensionType === '单一维度' ? '#1890ff' : '#666',
                                                padding: '8px 12px',
                                                borderRadius: '4px',
                                                width: '100%'
                                              }}
                                            >
                                              单一维度
                                            </AntButton>
                                            {(showAttributionAnalysis ? lockedAttributionSettings.crossDimensionEnabled : crossDimensionEnabled) && (
                                              <AntButton
                                                type={currentDimensionType === '交叉维度' ? 'primary' : 'text'}
                                                onClick={() => handleDimensionTypeChange('交叉维度')}
                                                style={{ 
                                                  textAlign: 'left',
                                                  border: 'none',
                                                  background: currentDimensionType === '交叉维度' ? '#e6f7ff' : 'transparent',
                                                  color: currentDimensionType === '交叉维度' ? '#1890ff' : '#666',
                                                  padding: '8px 12px',
                                                  borderRadius: '4px',
                                                  width: '100%'
                                                }}
                                              >
                                                交叉维度
                                              </AntButton>
                                            )}
                                </div>
                              </div>
                            </div>
                          )}

                                                                      {/* 指标归因左侧筛选区域 */}
                                            {tab === '指标归因' && (
                                              <div style={{ 
                                                width: '150px', 
                                                borderRight: '1px solid #f0f0f0', 
                                                paddingRight: '20px',
                                                flexShrink: 0
                                              }}>
                                                <div style={{ marginBottom: '20px' }}>
                                                  <div style={{ marginBottom: '12px', fontWeight: 'bold', fontSize: '14px' }}>指标类型</div>
                                                  <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                                                    {/* 显示指标类型，允许点击切换 */}
                                                    {(() => {
                                                      const lockedIndicatorTypes = showAttributionAnalysis ? lockedAttributionSettings.indicatorType : indicatorType;
                                                      return lockedIndicatorTypes.map(type => (
                                                        <AntButton
                                                          key={type}
                                                          type={currentIndicatorTab === type ? 'primary' : 'text'}
                                                          onClick={() => handleIndicatorTabChange(type)}
                                                          style={{ 
                                                            textAlign: 'left',
                                                            border: 'none',
                                                            background: currentIndicatorTab === type ? '#e6f7ff' : 'transparent',
                                                            color: currentIndicatorTab === type ? '#1890ff' : '#666',
                                                            padding: '8px 12px',
                                                            borderRadius: '4px',
                                                            width: '100%',
                                                            cursor: 'pointer'
                                                          }}
                                                        >
                                                          {type}
                                                        </AntButton>
                                                      ));
                                                    })()}
                                                  </div>
                                                </div>
                                              </div>
                                            )}



                          {/* 右侧内容区域 */}
                          <div style={{ 
                            flex: 1, 
                            minWidth: 0, // 这个很重要，允许flex项缩小到内容以下
                            overflow: 'hidden' // 防止内容溢出
                          }}>
                            {tab === '维度归因' && (
                              <div>
                                {/* 单一维度分析 */}
                                {currentDimensionType === '单一维度' && (
                                  <div>
                                    {/* 分析维度横向标签页 */}
                                    {getAvailableDimensionTabs().length > 0 && (
                                      <div style={{ borderBottom: '1px solid #d9d9d9', marginBottom: '20px' }}>
                                        {getAvailableDimensionTabs().map(dimension => (
                                          <AntButton
                                            key={dimension}
                                            type={currentDimensionTab === dimension ? 'primary' : 'text'}
                                            onClick={() => handleDimensionTabChange(dimension)}
                                            style={{ 
                                              marginRight: '16px',
                                              marginBottom: '8px',
                                              border: 'none',
                                              borderBottom: currentDimensionTab === dimension ? '2px solid #1890ff' : 'none',
                                              borderRadius: 0,
                                              background: 'none',
                                              color: currentDimensionTab === dimension ? '#1890ff' : '#666'
                                            }}
                                          >
                                            {dimension}
                                          </AntButton>
                                        ))}
                                      </div>
                                    )}

                                    {/* 显示当前选中的维度数据 */}
                                    {currentDimensionTab && (
                                      <div>
                                        <ResultTable
                                          sheets={getCurrentDisplayData()}
                                          marketData={rankingMarketData || marketData}
                                          onDownload={() => API.downloadExcel(filePath)}
                                          top3Data={renderTop3Data()}
                                          activeTab={currentDimensionTab}
                                          onTabChange={() => {}} // 禁用原有的标签页切换
                                          hideTabNavigation={true} // 隐藏原有的标签页导航
                                          selectedAnalysisDimensions={showAttributionAnalysis ? lockedAttributionSettings.selectedAnalysisDimensions : selectedAnalysisDimensions}
                                          currentDimension={currentDimensionTab}
                                          onDrillDown={handleDrillDown}
                                          onViewMetrics={handleViewMetrics}
                                          drillDownPath={drillDownPath}
                                          onGoBack={handleGoBack}
                                          onReset={handleResetDrillDown}
                                          onBreadcrumbClick={handleBreadcrumbClick}
                                          expandedRows={expandedRows}
                                          drillDownData={drillDownData}
                                          sheetsData={sheetsData}
                                          attributionSettings={{
                                            attributionMethod: (showAttributionAnalysis ? lockedAttributionSettings.attributionMethods : attributionMethods).includes('指标') ? 'metric_attribution' : null,
                                            selectedMetrics: attrIndex === '活动GMV' ? ['活动GMV', 'ROI'] : ['GMV'],
                                            enableMetricAttribution: (showAttributionAnalysis ? lockedAttributionSettings.attributionMethods : attributionMethods).includes('指标')
                                          }}
                                          dateParams={getDateParams()}
                                          // 传递当前筛选条件状态
                                          filterConditions={{
                                            subBrand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
                                            province: Array.isArray(province) ? province.join(',') : province,
                                            city: Array.isArray(city) ? city.join(',') : city,
                                            retailer: Array.isArray(retailer) ? retailer.join(',') : retailer,
                                            platform: Array.isArray(platform) ? platform.join(',') : platform,
                                            upc: getUpcByProductNames(selectedProduct),
                                            selectedProduct: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
                                            couponMechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
                                            couponThreshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
                                            couponDiscount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount
                                          }}
                                        />
                                      </div>
                                    )}

                                    {/* 如果没有选择任何维度 */}
                                    {!currentDimensionTab && (
                                      <div style={{ 
                                        padding: '40px 20px', 
                                        textAlign: 'center', 
                                        color: '#999'
                                      }}>
                                        请在归因设置中选择分析维度
                                      </div>
                                    )}
                                  </div>
                                )}

                                {/* 交叉维度分析 */}
                                {currentDimensionType === '交叉维度' && (showAttributionAnalysis ? lockedAttributionSettings.crossDimensionEnabled : crossDimensionEnabled) && (
                                  <div>
                                    {/* 交叉维度横向标签页 */}
                                    {getAvailableCrossTabs().length > 0 && (
                                      <div style={{ borderBottom: '1px solid #d9d9d9', marginBottom: '20px' }}>
                                        {getAvailableCrossTabs().map(crossTab => (
                                          <AntButton
                                            key={crossTab}
                                            type={currentCrossTab === crossTab ? 'primary' : 'text'}
                                            onClick={() => handleCrossTabChange(crossTab)}
                                            style={{ 
                                              marginRight: '16px',
                                              marginBottom: '8px',
                                              border: 'none',
                                              borderBottom: currentCrossTab === crossTab ? '2px solid #1890ff' : 'none',
                                              borderRadius: 0,
                                              background: 'none',
                                              color: currentCrossTab === crossTab ? '#1890ff' : '#666'
                                            }}
                                          >
                                            {crossTab}
                                          </AntButton>
                                        ))}
                                      </div>
                                    )}

                                    {/* 显示当前选中的交叉维度数据 */}
                                    {currentCrossTab && (
                                      <div>
                                        {sheetsData && sheetsData[currentCrossTab] ? (
                                          <ResultTable
                                            sheets={{ [currentCrossTab]: sheetsData[currentCrossTab] }}
                                            marketData={rankingMarketData || marketData}
                                            onDownload={() => API.downloadExcel(filePath)}
                                            top3Data={renderTop3Data()}
                                            activeTab={currentCrossTab}
                                            onTabChange={() => {}} // 禁用原有的标签页切换
                                            hideTabNavigation={true} // 隐藏原有的标签页导航
                                            isCrossDimension={true} // 标识这是交叉维度数据
                                            selectedAnalysisDimensions={showAttributionAnalysis ? lockedAttributionSettings.selectedAnalysisDimensions : selectedAnalysisDimensions}
                                            currentDimension={currentCrossTab}
                                            onDrillDown={handleDrillDown}
                                            onViewMetrics={handleViewMetrics}
                                            drillDownPath={drillDownPath}
                                            onGoBack={handleGoBack}
                                            onReset={handleResetDrillDown}
                                            sheetsData={sheetsData}
                                            onBreadcrumbClick={handleBreadcrumbClick}
                                            expandedRows={expandedRows}
                                            drillDownData={drillDownData}
                                            attributionSettings={{
                                              attributionMethod: (showAttributionAnalysis ? lockedAttributionSettings.attributionMethods : attributionMethods).includes('指标') ? 'metric_attribution' : null,
                                              selectedMetrics: attrIndex === '活动GMV' ? ['活动GMV', 'ROI'] : ['GMV'],
                                              enableMetricAttribution: (showAttributionAnalysis ? lockedAttributionSettings.attributionMethods : attributionMethods).includes('指标')
                                            }}
                                            dateParams={getDateParams()}
                                            // 传递当前筛选条件状态
                                            filterConditions={{
                                              subBrand: Array.isArray(subBrand) ? subBrand.join(',') : subBrand,
                                              province: Array.isArray(province) ? province.join(',') : province,
                                              city: Array.isArray(city) ? city.join(',') : city,
                                              retailer: Array.isArray(retailer) ? retailer.join(',') : retailer,
                                              platform: Array.isArray(platform) ? platform.join(',') : platform,
                                              upc: getUpcByProductNames(selectedProduct),
                                              selectedProduct: Array.isArray(selectedProduct) ? (selectedProduct.includes('全部') ? '' : selectedProduct.join(',')) : '',
                                              couponMechanism: Array.isArray(selectedCouponMechanism) ? selectedCouponMechanism.join(',') : selectedCouponMechanism,
                                              couponThreshold: Array.isArray(selectedCouponThreshold) ? selectedCouponThreshold.join(',') : selectedCouponThreshold,
                                              couponDiscount: Array.isArray(selectedCouponDiscount) ? selectedCouponDiscount.join(',') : selectedCouponDiscount
                                            }}
                                          />
                                        ) : (
                                          <div style={{ 
                                            padding: '40px 20px', 
                                            textAlign: 'center', 
                                            color: '#999',
                                            border: '1px dashed #d9d9d9',
                                            borderRadius: '4px'
                                          }}>
                                            {currentCrossTab} 暂无数据
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}

                                                                        {tab === '指标归因' && (
                                              <div>
                                                {/* 指标关系内容 */}
                                                <div>
                                                  {(() => {
                                                    const lockedIndicatorTypes = showAttributionAnalysis ? lockedAttributionSettings.indicatorType : indicatorType;
                                                    
                                                    if (lockedIndicatorTypes.length === 0) {
                                                      return (
                                                        <div style={{ 
                                                          padding: '40px 20px', 
                                                          textAlign: 'center', 
                                                          color: '#999',
                                                          border: '1px dashed #d9d9d9',
                                                          borderRadius: '4px'
                                                        }}>
                                                          请选择指标类型
                                                        </div>
                                                      );
                                                    }
                                                    
                                                    // 如果没有选中任何标签页且没有可用的指标类型，显示提示
                                                    if (!currentIndicatorTab) {
                                                      return (
                                                        <div style={{ 
                                                          padding: '40px 20px', 
                                                          textAlign: 'center', 
                                                          color: '#999',
                                                          border: '1px dashed #d9d9d9',
                                                          borderRadius: '4px'
                                                        }}>
                                                          请选择指标类型标签页
                                                        </div>
                                                      );
                                                    }
                                                    
                                                    return (
                                                      <div>
                                                        {/* 显示供给向指标内容 */}
                                                        {currentIndicatorTab === '供给向' && (
                                                          <div>
                                                            {/* 只显示新的供给向指标组件 */}
                                                            {supplySideData && analysisType?.is_supply_side && (
                                                              <SupplySideTable data={supplySideData} />
                                                            )}
                                                          </div>
                                                        )}
                                                        
                                                                                                {/* 显示营销向指标内容 */}
                                        {currentIndicatorTab === '营销向' && (
                                          <div>
                                            {/* 显示营销向指标表格 */}
                                            {marketingSideData && analysisType?.has_marketing_side && (
                                              <MarketingSideTable data={marketingSideData} />
                                            )}
                                            
                                            {/* 如果没有营销向数据，显示指标关系公式 */}
                                            {(!marketingSideData || !analysisType?.has_marketing_side) && (
                                              <div style={{ marginBottom: '20px' }}>
                                                <div style={{ 
                                                  fontWeight: 'bold', 
                                                  marginBottom: '8px', 
                                                  color: '#1890ff',
                                                  fontSize: '14px'
                                                }}>
                                                  营销向指标关系：
                                                </div>
                                                {getIndicatorFormulas('营销向').map((formula, formulaIndex) => (
                                                  <div key={formulaIndex} style={{ 
                                                    marginBottom: '8px', 
                                                    padding: '8px 12px', 
                                                    backgroundColor: '#f5f5f5', 
                                                    borderRadius: '4px',
                                                    fontFamily: 'monospace',
                                                    fontSize: '14px'
                                                  }}>
                                                    {formula}
                                                  </div>
                                                ))}
                                              </div>
                                            )}
                                          </div>
                                        )}
                                                      </div>
                                                    );
                                                  })()}
                                                </div>
                              </div>
                            )}


                          </div>
                        </div>
                      </div>
                    )
                  }))}
                />
              </div>
            )}

            {/* 如果没有选择任何归因方式 */}
            {getAvailableAttributionTabs().length === 0 && (
              <div style={{ 
                padding: '40px 20px', 
                textAlign: 'center', 
                color: '#999'
              }}>
                请在归因设置中选择归因方式
              </div>
            )}
          </Card>
        )}
      </div>

      {/* 贡献度说明和AI归因分析功能只在获取数据后显示 */}
      {showAttributionAnalysis && (
        <>
          {/* 贡献度说明 */}
          <div style={{ fontSize: '14px', color: '#86868b', marginBottom: '20px' }}>
            {/* <p style={{ margin: 0 }}>贡献度：某维度数据的波动对整体指标波动的贡献比例，其中当大盘波动为正向时，各项贡献度之和为+100%；当大盘波动为负向时，各项贡献度之和为-100%，反映其对整体波动的相对影响程度。</p> */}
          </div>

          <ProgressBarIsolated 
            isLoading={isLoading} 
            onComplete={() => {
              // 可以在这里添加完成后的逻辑
            }}
          />

          <Card className="mb-4">
            <h5 style={{ margin: '0 0 8px 0', padding: 0 }}>AI归因结论</h5> {/* 标题位置往左上移 */}
            <div id="output" style={{
              width: '100%',
              height: '400px',
              fontFamily: 'monospace',
              whiteSpace: 'pre-wrap',
              overflowY: 'auto',
              overflowX: 'auto',
              borderRadius: '12px',
              padding: '20px',
              backgroundColor: '#ffffff',
              lineHeight: '1.5',
              fontSize: '15px',
              maxWidth: '100%'
            }}
            dangerouslySetInnerHTML={{ __html: formatAttributionOutput(output) }}
            >
            </div>
          </Card>
        </>
      )}
    </Container>
  );
}

export default App;