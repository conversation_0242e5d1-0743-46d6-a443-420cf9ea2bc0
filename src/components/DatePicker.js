import React, { useEffect } from 'react';
import Flatpickr from 'react-flatpickr';
import { Mandarin } from 'flatpickr/dist/l10n/zh';
import 'flatpickr/dist/flatpickr.min.css';

const DatePicker = ({ id, placeholder, value, onChange, className, style }) => {
  // 获取昨天的日期作为最大日期
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);

  const options = {
    locale: Mandarin,
    dateFormat: 'Y-m-d',
    maxDate: yesterday,
    disableMobile: true,
    allowInput: true
  };

  useEffect(() => {
    // 避免日期选择器抛出React中的事件处理警告
    return () => {};
  }, []);

  return (
    <div style={{ position: 'relative', width: '200px', ...(style || {}) }}>
      <Flatpickr
        id={id}
        placeholder={placeholder}
        options={options}
        value={value}
        onChange={(dates) => {
          if (dates.length > 0) {
            onChange(dates[0]);
          } else {
            onChange(null);
          }
        }}
        className={`form-control date-picker ${className || ''}`}
        style={{
          width: '100%',
          border: '1px solid #d2d2d7',
          borderRadius: '8px',
          padding: '8px 12px',
          paddingRight: '30px',
          fontSize: '14px',
          height: '36px',
          lineHeight: '1.5',
          backgroundColor: '#ffffff',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
          transition: 'all 0.2s ease'
        }}
      />
      <i
        className="bi bi-calendar3"
        style={{
          position: 'absolute',
          right: '10px',
          top: '50%',
          transform: 'translateY(-50%)',
          pointerEvents: 'none',
          color: '#86868b'
        }}
      />
    </div>
  );
};

export default DatePicker; 