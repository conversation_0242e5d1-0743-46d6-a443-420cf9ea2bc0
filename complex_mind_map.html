<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="b18c0377a0f54674a5fcb4e19a713a99" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_b18c0377a0f54674a5fcb4e19a713a99 = echarts.init(
            document.getElementById('b18c0377a0f54674a5fcb4e19a713a99'), 'white', {renderer: 'canvas'});
        var option_b18c0377a0f54674a5fcb4e19a713a99 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "tree",
            "name": "\u590d\u6742\u8111\u56fe",
            "data": [
                {
                    "name": "\u4e2d\u5fc3\u4e3b\u9898",
                    "children": [
                        {
                            "name": "\u5b50\u4e3b\u9898 A",
                            "children": [
                                {
                                    "name": "\u5b50\u4e3b\u9898 A1"
                                },
                                {
                                    "name": "\u5b50\u4e3b\u9898 A2",
                                    "children": [
                                        {
                                            "name": "\u5b50\u4e3b\u9898 A2-1"
                                        },
                                        {
                                            "name": "\u5b50\u4e3b\u9898 A2-2"
                                        }
                                    ]
                                },
                                {
                                    "name": "\u5b50\u4e3b\u9898 A3"
                                }
                            ],
                            "collapsed": "false"
                        },
                        {
                            "name": "\u5b50\u4e3b\u9898 B",
                            "children": [
                                {
                                    "name": "\u5b50\u4e3b\u9898 B1",
                                    "children": [
                                        {
                                            "name": "\u5b50\u4e3b\u9898 B1-1"
                                        },
                                        {
                                            "name": "\u5b50\u4e3b\u9898 B1-2"
                                        }
                                    ]
                                },
                                {
                                    "name": "\u5b50\u4e3b\u9898 B2"
                                }
                            ]
                        },
                        {
                            "name": "\u5b50\u4e3b\u9898 C",
                            "children": [
                                {
                                    "name": "\u5b50\u4e3b\u9898 C1"
                                },
                                {
                                    "name": "\u5b50\u4e3b\u9898 C2"
                                },
                                {
                                    "name": "\u5b50\u4e3b\u9898 C3"
                                }
                            ],
                            "collapsed": "false"
                        }
                    ]
                }
            ],
            "zoom": 1,
            "symbol": "emptyCircle",
            "symbolSize": 7,
            "edgeShape": "curve",
            "edgeForkPosition": "50%",
            "roam": false,
            "expandAndCollapse": true,
            "layout": "orthogonal",
            "orient": "TB",
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "leaves": {},
            "selectedMode": false
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u590d\u6742\u8111\u56fe\u793a\u4f8b",
            "target": "blank",
            "subtext": "\u4f7f\u7528 Pyecharts",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "toolbox": {
        "show": true,
        "orient": "horizontal",
        "itemSize": 15,
        "itemGap": 10,
        "left": "80%",
        "feature": {
            "saveAsImage": {
                "type": "png",
                "backgroundColor": "auto",
                "connectedBackgroundColor": "#fff",
                "show": true,
                "title": "\u4fdd\u5b58\u4e3a\u56fe\u7247",
                "pixelRatio": 1
            },
            "restore": {
                "show": true,
                "title": "\u8fd8\u539f"
            },
            "dataView": {
                "show": true,
                "title": "\u6570\u636e\u89c6\u56fe",
                "readOnly": false,
                "lang": [
                    "\u6570\u636e\u89c6\u56fe",
                    "\u5173\u95ed",
                    "\u5237\u65b0"
                ],
                "backgroundColor": "#fff",
                "textareaColor": "#fff",
                "textareaBorderColor": "#333",
                "textColor": "#000",
                "buttonColor": "#c23531",
                "buttonTextColor": "#fff"
            },
            "dataZoom": {
                "show": true,
                "title": {
                    "zoom": "\u533a\u57df\u7f29\u653e",
                    "back": "\u533a\u57df\u7f29\u653e\u8fd8\u539f"
                },
                "icon": {},
                "filterMode": "filter"
            },
            "magicType": {
                "show": true,
                "type": [
                    "line",
                    "bar",
                    "stack",
                    "tiled"
                ],
                "title": {
                    "line": "\u5207\u6362\u4e3a\u6298\u7ebf\u56fe",
                    "bar": "\u5207\u6362\u4e3a\u67f1\u72b6\u56fe",
                    "stack": "\u5207\u6362\u4e3a\u5806\u53e0",
                    "tiled": "\u5207\u6362\u4e3a\u5e73\u94fa"
                },
                "icon": {}
            }
        }
    }
};
        chart_b18c0377a0f54674a5fcb4e19a713a99.setOption(option_b18c0377a0f54674a5fcb4e19a713a99);
    </script>
</body>
</html>
